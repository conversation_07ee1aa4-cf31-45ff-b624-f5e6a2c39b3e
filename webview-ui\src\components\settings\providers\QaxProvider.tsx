import { ApiConfiguration } from "@shared/api"
import { VSCodeTextField, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
import { memo, useState, useEffect } from "react"
import { vscode } from "../../../utils/vscode"

interface QaxProviderProps {
	apiConfiguration: ApiConfiguration
	handleInputChange: (field: keyof ApiConfiguration) => (event: any) => void
	showModelOptions: boolean
	isPopup?: boolean
}

const QaxProvider = ({ apiConfiguration, handleInputChange, showModelOptions, isPopup }: QaxProviderProps) => {
	const [models, setModels] = useState<[string, string][]>([])
	const [loading, setLoading] = useState(false)

	useEffect(() => {
		if (apiConfiguration?.qaxApiKey) {
			setLoading(true)
			vscode.postMessage({
				type: "fetchQaxModels",
				apiKey: apiConfiguration.qaxApiKey,
			})

			const handleMessage = (event: MessageEvent) => {
				const message = event.data
				if (message.type === "qaxModelsResponse") {
					if (message.data) {
						setModels(message.data)
					}
					setLoading(false)
				} else if (message.type === "qaxModelsError") {
					console.error("Error fetching models:", message.error)
					setLoading(false)
				}
			}

			window.addEventListener("message", handleMessage)
			return () => window.removeEventListener("message", handleMessage)
		}
	}, [apiConfiguration?.qaxApiKey])

	return (
		<div>
			<VSCodeTextField
				value={apiConfiguration?.qaxApiKey || ""}
				style={{ width: "100%" }}
				type="password"
				onInput={handleInputChange("qaxApiKey")}
				placeholder="Enter API Key...">
				<span style={{ fontWeight: 500 }}>QAX API Key</span>
			</VSCodeTextField>
			<p
				style={{
					fontSize: "12px",
					marginTop: 3,
					color: "var(--vscode-descriptionForeground)",
				}}>
				This key is stored locally and only used to make API requests from this extension.
			</p>

			{showModelOptions && (
				<VSCodeDropdown
					value={apiConfiguration?.qaxModelId || ""}
					style={{ width: "100%" }}
					onChange={handleInputChange("qaxModelId")}
					disabled={loading || models.length === 0}>
					<span style={{ fontWeight: 500 }}>Model</span>
					{loading ? (
						<VSCodeOption value="">Loading models...</VSCodeOption>
					) : models.length > 0 ? (
						models.map(([displayName, modelId]) => (
							<VSCodeOption key={modelId} value={modelId}>
								{displayName}
							</VSCodeOption>
						))
					) : (
						<VSCodeOption value="">No models available</VSCodeOption>
					)}
				</VSCodeDropdown>
			)}
			<p
				style={{
					fontSize: "12px",
					marginTop: 3,
					color: "var(--vscode-descriptionForeground)",
				}}>
				Select the model to use for requests.
			</p>
		</div>
	)
}

export default memo(QaxProvider)
