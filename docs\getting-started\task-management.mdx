---
title: "Task Management in Cline"
description: "Learn how to effectively manage your task history, use favorites, and organize your work in Cline."
---

# Task Management

As you use Cline, you'll accumulate many tasks over time. The task management system helps you organize, filter, search, and clean up your task history to keep your workspace efficient.

## Accessing Task History

You can access your task history by:

1. Clicking on the "History" button in the Cline sidebar
2. Using the command palette to search for "Cline: Show Task History"

## Task History Features

The task history view provides several powerful features:

### Searching and Filtering

-   **Search Bar**: Use the fuzzy search at the top to quickly find tasks by content
-   **Sort Options**: Sort tasks by:
    -   Newest (default)
    -   Oldest
    -   Most Expensive (highest API cost)
    -   Most Tokens (highest token usage)
    -   Most Relevant (when searching)
-   **Favorites Filter**: Toggle to show only favorited tasks

### Task Actions

Each task in the history view has several actions available:

-   **Open**: Click on a task to reopen it in the Cline chat
-   **Favorite**: Click the star icon to mark a task as a favorite
-   **Delete**: Remove individual tasks (favorites are protected from deletion)
-   **Export**: Export a task's conversation to markdown

## ⭐ Task Favorites

The favorites feature allows you to mark important tasks that you want to preserve and find quickly.

### How Favorites Work

-   **Marking Favorites**: Click the star icon next to any task to toggle its favorite status
-   **Protection**: Favorited tasks are protected from individual and bulk deletion operations (can be overridden)
-   **Filtering**: Use the favorites filter to quickly access your important tasks

## Batch Operations

The task history view supports several batch operations:

-   **Select Multiple**: Use the checkboxes to select multiple tasks
-   **Select All/None**: Quickly select or deselect all tasks
-   **Delete Selected**: Remove all selected tasks
-   **Delete All**: Remove all tasks from history (favorites are preserved unless you choose to include them)

## Best Practices

1. **Favorite Important Tasks**: Mark reference tasks or frequently accessed conversations as favorites
2. **Regular Cleanup**: Periodically remove old or unused tasks to improve performance
3. **Use Search**: Leverage the fuzzy search to quickly find specific conversations
4. **Export Valuable Tasks**: Export important tasks to markdown for external reference

Task management helps you maintain an organized workflow when using Cline, allowing you to quickly find past conversations, preserve important work, and keep your history clean and efficient.
