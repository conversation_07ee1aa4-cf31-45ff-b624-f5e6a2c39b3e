# Autocomplete Provider 条件显示 UI

## 实现逻辑

根据用户选择的 Provider 类型，动态显示对应的配置字段：

### 条件显示逻辑
```tsx
{localSettings.provider === "fim" ? (
  // 显示 FIM 相关配置
  <>
    <ApiKeyField
      value={localSettings.fim?.apiKey || ""}
      onChange={handleInputChange("fim.apiKey")}
      providerName="FIM API"
      placeholder="Enter your FIM API key..."
    />
    
    <BaseUrlField
      value={localSettings.fim?.baseUrl || ""}
      onChange={handleInputChange("fim.baseUrl")}
      placeholder="https://your-fim-api.com/v1"
      label="FIM Base URL"
    />
  </>
) : (
  // 显示 OpenAI Compatible 相关配置
  <>
    <ApiKeyField
      value={localSettings.apiKey || ""}
      onChange={handleInputChange("apiKey")}
      providerName="OpenAI Compatible API"
      placeholder="Enter your API key..."
    />
    
    <BaseUrlField
      value={localSettings.apiBaseUrl || ""}
      onChange={handleInputChange("apiBaseUrl")}
      placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
      label="API Base URL"
    />
  </>
)}
```

## UI 行为

### 选择 "OpenAI Compatible" 时
显示字段：
- **API Key**: OpenAI Compatible API 的密钥
- **API Base URL**: OpenAI Compatible API 的基础 URL
  - 占位符：`https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)`
  - 标签：`API Base URL`

### 选择 "FIM (Fill in the Middle)" 时
显示字段：
- **API Key**: FIM API 的密钥
- **FIM Base URL**: FIM API 的基础 URL
  - 占位符：`https://your-fim-api.com/v1`
  - 标签：`FIM Base URL`

## 配置页面结构

```
┌─ Autocomplete Settings ─────────────────────────────┐
│                                                     │
│ ☑ Enable QAX Autocomplete                          │
│                                                     │
│ Provider: [OpenAI Compatible ▼]                    │
│                                                     │
│ ┌─ 根据 Provider 选择动态显示 ─────────────────────┐ │
│ │                                                 │ │
│ │ 如果选择 "OpenAI Compatible":                   │ │
│ │   API Key: [Enter your API key...]             │ │
│ │   API Base URL: [https://api.openrouter.ai...] │ │
│ │                                                 │ │
│ │ 如果选择 "FIM":                                 │ │
│ │   API Key: [Enter your FIM API key...]         │ │
│ │   FIM Base URL: [https://your-fim-api.com/v1]  │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ Model ID: [google/gemini-2.5-flash-preview-05-20]  │
│ Max Tokens: [1000]                                  │
│ Temperature: [0.1]                                  │
│ Request Timeout (ms): [30000]                       │
│ Debounce Time (ms): [300]                           │
│ ☐ Use Prompt Cache                                  │
│                                                     │
│ [Save Settings] [Reset]                             │
└─────────────────────────────────────────────────────┘
```

## 用户体验

### 优势
1. **清晰的界面**：只显示当前选择 Provider 相关的配置
2. **减少混淆**：避免用户同时看到两套 API 配置
3. **聚焦配置**：用户专注于当前选择的 Provider 配置
4. **动态响应**：切换 Provider 时界面立即更新

### 交互流程
1. 用户选择 Provider（OpenAI Compatible 或 FIM）
2. 界面动态显示对应的 API Key 和 Base URL 字段
3. 用户填写相应的配置信息
4. 保存设置

## 配置数据结构

### OpenAI Compatible 配置
```typescript
{
  provider: "openai",
  apiKey: "sk-xxx...",
  apiBaseUrl: "https://api.openrouter.ai/api/v1",
  // ... 其他通用设置
}
```

### FIM 配置
```typescript
{
  provider: "fim",
  fim: {
    apiKey: "fim-key-xxx...",
    baseUrl: "https://your-fim-api.com/v1"
  },
  // ... 其他通用设置
}
```

## 验证逻辑

### 根据 Provider 验证相应字段
```typescript
if (settings.provider === "fim") {
  // 验证 FIM 配置
  if (!settings.fim?.apiKey) {
    errors.push("FIM API key is required when FIM provider is enabled")
  }
  if (!settings.fim?.baseUrl) {
    errors.push("FIM base URL is required when FIM provider is enabled")
  }
} else {
  // 验证 OpenAI 配置
  if (!settings.apiKey) {
    errors.push("API key is required when autocomplete is enabled")
  }
}
```

## 实现特点

1. **条件渲染**：使用三元运算符根据 provider 选择显示不同内容
2. **独立配置**：两种 Provider 的配置完全独立存储
3. **即时切换**：切换 Provider 时界面立即响应
4. **配置保持**：切换 Provider 不会丢失已填写的配置
5. **清晰标识**：不同 Provider 的字段有明确的标签和占位符

这种实现方式提供了清晰、直观的用户界面，让用户能够专注于当前选择的 Provider 配置！
