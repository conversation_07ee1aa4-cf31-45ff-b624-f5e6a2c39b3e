import { V<PERSON>ode<PERSON>utton, VSCodeProgressRing, VSCodeDivider } from "@vscode/webview-ui-toolkit/react"
import { memo, useCallback, useEffect, useState } from "react"
import { Database, RefreshCw, Trash2, Play, Pause, AlertCircle, CheckCircle, Clock } from "lucide-react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { AstDatabaseServiceClient } from "@/services/grpc-client"
import { EmptyRequest } from "@shared/proto/common"
import { StartScanRequest, ClearDatabaseRequest } from "@shared/proto/astdb"

interface ScanProgress {
	totalFiles: number
	processedFiles: number
	currentFile: string
	errors: string[]
	status: "scanning" | "parsing" | "indexing" | "complete" | "error" | "idle"
	startTime: Date
	elapsedMs: number
	estimatedRemainingMs: number
	filesPerSecond: number
	definitionsFound: number
	usagesFound: number
	bytesProcessed: number
}

interface DatabaseStatus {
	astate: "ready" | "indexing" | "error" | "initializing"
	filesTotal: number
	filesUnparsed: number
	astIndexFilesTotal: number
	astIndexSymbolsTotal: number
	astIndexUsagesTotal: number
	astMaxFilesHit: boolean
	lastUpdated?: Date
	dbSizeBytes?: number
	uniqueFiles?: number
	averageDefinitionsPerFile?: number
}

const CodebaseSettingsSection = () => {
	const { filePaths } = useExtensionState()
	const [scanProgress, setScanProgress] = useState<ScanProgress | null>(null)
	const [databaseStatus, setDatabaseStatus] = useState<DatabaseStatus | null>(null)
	const [isScanning, setIsScanning] = useState(false)
	const [scanErrors, setScanErrors] = useState<string[]>([])
	const [showErrors, setShowErrors] = useState(false)

	// Load initial database status
	useEffect(() => {
		loadDatabaseStatus()
	}, [])

	const loadDatabaseStatus = useCallback(async () => {
		try {
			console.log("Loading AST database status...")
			const response = await AstDatabaseServiceClient.getDatabaseStatus(EmptyRequest.create({}))
			console.log("AST database status response:", response)

			if (response.status) {
				setDatabaseStatus({
					astate: response.status.astate as "ready" | "indexing" | "error" | "initializing",
					filesTotal: response.status.filesTotal,
					filesUnparsed: response.status.filesUnparsed,
					astIndexFilesTotal: response.status.astIndexFilesTotal,
					astIndexSymbolsTotal: response.status.astIndexSymbolsTotal,
					astIndexUsagesTotal: response.status.astIndexUsagesTotal,
					astMaxFilesHit: response.status.astMaxFilesHit,
					lastUpdated: response.status.lastUpdated ? new Date(response.status.lastUpdated) : undefined,
					dbSizeBytes: response.status.dbSizeBytes,
					uniqueFiles: response.status.uniqueFiles,
					averageDefinitionsPerFile: response.status.averageDefinitionsPerFile
				})
			} else {
				// No database status returned - this means database is not initialized
				setDatabaseStatus(null)
			}
		} catch (error) {
			console.error("Failed to load database status:", error)
			// Check if this is a "database not initialized" error vs a real error
			const errorMessage = error?.toString() || ""
			if (errorMessage.includes("not initialized") || errorMessage.includes("not found")) {
				// Database not initialized - this is normal for first time use
				setDatabaseStatus(null)
				setScanErrors([]) // Clear any previous errors
			} else {
				// Real error occurred
				setScanErrors([`Failed to load database status: ${error}`])
				setDatabaseStatus({
					astate: "error",
					filesTotal: 0,
					filesUnparsed: 0,
					astIndexFilesTotal: 0,
					astIndexSymbolsTotal: 0,
					astIndexUsagesTotal: 0,
					astMaxFilesHit: false
				})
			}
		}
	}, [])

	const handleStartScan = useCallback(async () => {
		setIsScanning(true)
		setScanErrors([])
		setScanProgress({
			totalFiles: 0,
			processedFiles: 0,
			currentFile: "",
			errors: [],
			status: "scanning",
			startTime: new Date(),
			elapsedMs: 0,
			estimatedRemainingMs: 0,
			filesPerSecond: 0,
			definitionsFound: 0,
			usagesFound: 0,
			bytesProcessed: 0
		})

		try {
			console.log("Starting workspace scan...")
			// Get current workspace path from the first file path if available
			// In webview environment, we cannot use process.cwd(), so we need to get workspace path differently
			let workspacePath = ""
			if (filePaths.length > 0) {
				// Extract workspace path from the first file path
				const pathParts = filePaths[0].split('/')
				workspacePath = pathParts.slice(0, -1).join('/')
			}

			// If we still don't have a workspace path, we need to request it from the extension
			if (!workspacePath) {
				console.warn("No workspace path available from file paths, using empty string")
				// The backend service should handle this case and use vscode.workspace.workspaceFolders
			}

			await AstDatabaseServiceClient.startWorkspaceScan(StartScanRequest.create({
				workspacePath: workspacePath,
				options: {
					maxFiles: 1000,
					includeExtensions: ["js", "jsx", "ts", "tsx", "py", "rs", "go", "c", "h", "cpp", "hpp", "cs", "rb", "java", "php", "swift", "kt"],
					excludePatterns: ["node_modules/**", ".git/**", "dist/**", "build/**"]
				}
			}))

			console.log("Workspace scan started successfully")

			// For now, use mock scanning process to show progress
			// In a real implementation, we would listen to scan progress events
			mockScanProcess()
		} catch (error) {
			console.error("Failed to start scan:", error)
			setScanErrors([`Failed to start scan: ${error}`])
			setIsScanning(false)
		}
	}, [])

	const mockScanProcess = () => {
		let progress = 0
		const totalFiles = 150
		const startTime = Date.now()
		
		const interval = setInterval(() => {
			progress += Math.random() * 5 + 1
			const elapsedMs = Date.now() - startTime
			const filesPerSecond = progress / (elapsedMs / 1000)
			const estimatedRemainingMs = filesPerSecond > 0 ? ((totalFiles - progress) / filesPerSecond) * 1000 : 0
			
			setScanProgress({
				totalFiles,
				processedFiles: Math.min(progress, totalFiles),
				currentFile: `src/components/file${Math.floor(progress)}.tsx`,
				errors: [],
				status: progress >= totalFiles ? "complete" : "parsing",
				startTime: new Date(startTime),
				elapsedMs,
				estimatedRemainingMs,
				filesPerSecond,
				definitionsFound: Math.floor(progress * 8.3),
				usagesFound: Math.floor(progress * 22.7),
				bytesProcessed: Math.floor(progress * 15000)
			})
			
			if (progress >= totalFiles) {
				clearInterval(interval)
				setIsScanning(false)
				loadDatabaseStatus() // Refresh status after scan
			}
		}, 200)
	}

	const handleClearDatabase = useCallback(async () => {
		if (!confirm("Are you sure you want to clear the AST database? This action cannot be undone.")) {
			return
		}

		try {
			console.log("Clearing AST database...")
			await AstDatabaseServiceClient.clearDatabase(ClearDatabaseRequest.create({}))
			console.log("AST database cleared successfully")

			setDatabaseStatus(null)
			setScanProgress(null)

			// Reload status to confirm clearing
			setTimeout(() => {
				loadDatabaseStatus()
			}, 1000)
		} catch (error) {
			console.error("Failed to clear database:", error)
			setScanErrors([`Failed to clear database: ${error}`])
		}
	}, [loadDatabaseStatus])

	const formatBytes = (bytes: number) => {
		if (bytes === 0) return "0 B"
		const k = 1024
		const sizes = ["B", "KB", "MB", "GB"]
		const i = Math.floor(Math.log(bytes) / Math.log(k))
		return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i]
	}

	const formatDuration = (ms: number) => {
		const seconds = Math.floor(ms / 1000)
		const minutes = Math.floor(seconds / 60)
		const hours = Math.floor(minutes / 60)
		
		if (hours > 0) return `${hours}h ${minutes % 60}m`
		if (minutes > 0) return `${minutes}m ${seconds % 60}s`
		return `${seconds}s`
	}

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "ready": return <CheckCircle className="w-4 h-4 text-green-500" />
			case "indexing": case "scanning": case "parsing": return <Clock className="w-4 h-4 text-blue-500" />
			case "error": return <AlertCircle className="w-4 h-4 text-red-500" />
			default: return <Database className="w-4 h-4" />
		}
	}

	const getProgressPercentage = () => {
		if (!scanProgress || scanProgress.totalFiles === 0) return 0
		return Math.round((scanProgress.processedFiles / scanProgress.totalFiles) * 100)
	}

	return (
		<div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
			{/* Header */}
			<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
				<Database className="w-5 h-5" />
				<h3 style={{ margin: 0, fontSize: "16px", fontWeight: "600" }}>
					AST Database
				</h3>
			</div>

			{/* Database Status */}
			{!databaseStatus ? (
				<div style={{
					padding: "16px",
					border: "1px solid var(--vscode-panel-border)",
					borderRadius: "4px",
					backgroundColor: "var(--vscode-editor-background)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "12px" }}>
						<Database className="w-4 h-4 text-gray-500" />
						<span style={{ fontWeight: "500" }}>
							No AST Database Found
						</span>
					</div>
					<div style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)", marginBottom: "12px" }}>
						The AST database has not been created yet. Click "Scan Workspace" to build an index of your codebase for enhanced autocomplete and code navigation.
					</div>
					<div style={{ fontSize: "12px", color: "var(--vscode-descriptionForeground)" }}>
						This will analyze your code files and create a searchable index of definitions, usages, and symbols.
					</div>
				</div>
			) : (
				<div style={{
					padding: "16px",
					border: "1px solid var(--vscode-panel-border)",
					borderRadius: "4px",
					backgroundColor: "var(--vscode-editor-background)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "12px" }}>
						{getStatusIcon(databaseStatus.astate)}
						<span style={{ fontWeight: "500" }}>
							Status: {databaseStatus.astate.charAt(0).toUpperCase() + databaseStatus.astate.slice(1)}
						</span>
					</div>
					
					<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "12px", fontSize: "14px" }}>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Files Indexed</div>
							<div style={{ fontWeight: "500" }}>{databaseStatus.astIndexFilesTotal}</div>
						</div>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Definitions</div>
							<div style={{ fontWeight: "500" }}>{databaseStatus.astIndexSymbolsTotal}</div>
						</div>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Usages</div>
							<div style={{ fontWeight: "500" }}>{databaseStatus.astIndexUsagesTotal}</div>
						</div>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Database Size</div>
							<div style={{ fontWeight: "500" }}>
								{databaseStatus.dbSizeBytes ? formatBytes(databaseStatus.dbSizeBytes) : "Unknown"}
							</div>
						</div>
					</div>
					
					{databaseStatus.lastUpdated && (
						<div style={{ marginTop: "12px", fontSize: "12px", color: "var(--vscode-descriptionForeground)" }}>
							Last updated: {databaseStatus.lastUpdated.toLocaleString()}
						</div>
					)}
				</div>
			)}

			{/* Scan Progress */}
			{scanProgress && (
				<div style={{ 
					padding: "16px", 
					border: "1px solid var(--vscode-panel-border)", 
					borderRadius: "4px",
					backgroundColor: "var(--vscode-editor-background)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "12px" }}>
						{isScanning && <VSCodeProgressRing />}
						<span style={{ fontWeight: "500" }}>
							{scanProgress.status === "complete" ? "Scan Complete" : "Scanning Workspace"}
						</span>
						<span style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)" }}>
							{getProgressPercentage()}%
						</span>
					</div>
					
					{/* Progress Bar */}
					<div style={{ 
						width: "100%", 
						height: "8px", 
						backgroundColor: "var(--vscode-progressBar-background)",
						borderRadius: "4px",
						marginBottom: "12px",
						overflow: "hidden"
					}}>
						<div style={{
							width: `${getProgressPercentage()}%`,
							height: "100%",
							backgroundColor: "var(--vscode-progressBar-foreground)",
							transition: "width 0.3s ease"
						}} />
					</div>
					
					{/* Progress Details */}
					<div style={{ fontSize: "14px", display: "flex", flexDirection: "column", gap: "4px" }}>
						<div>
							Files: {scanProgress.processedFiles} / {scanProgress.totalFiles}
							{scanProgress.filesPerSecond > 0 && (
								<span style={{ color: "var(--vscode-descriptionForeground)", marginLeft: "8px" }}>
									({scanProgress.filesPerSecond.toFixed(1)} files/s)
								</span>
							)}
						</div>
						{scanProgress.currentFile && (
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>
								Current: {scanProgress.currentFile}
							</div>
						)}
						{scanProgress.estimatedRemainingMs > 0 && (
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>
								ETA: {formatDuration(scanProgress.estimatedRemainingMs)}
							</div>
						)}
						<div>
							Found: {scanProgress.definitionsFound} definitions, {scanProgress.usagesFound} usages
						</div>
					</div>
				</div>
			)}

			{/* Action Buttons */}
			<div style={{ display: "flex", gap: "12px", flexWrap: "wrap" }}>
				<VSCodeButton 
					appearance="primary"
					onClick={handleStartScan}
					disabled={isScanning}
					style={{ display: "flex", alignItems: "center", gap: "6px" }}
				>
					{isScanning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
					{isScanning ? "Scanning..." : databaseStatus ? "Rescan Workspace" : "Scan Workspace"}
				</VSCodeButton>
				
				<VSCodeButton 
					appearance="secondary"
					onClick={loadDatabaseStatus}
					style={{ display: "flex", alignItems: "center", gap: "6px" }}
				>
					<RefreshCw className="w-4 h-4" />
					Refresh Status
				</VSCodeButton>
				
				{databaseStatus && (
					<VSCodeButton 
						appearance="secondary"
						onClick={handleClearDatabase}
						style={{ display: "flex", alignItems: "center", gap: "6px" }}
					>
						<Trash2 className="w-4 h-4" />
						Clear Database
					</VSCodeButton>
				)}
			</div>

			{/* Error Display */}
			{scanErrors.length > 0 && (
				<div style={{ 
					padding: "12px", 
					border: "1px solid var(--vscode-errorForeground)", 
					borderRadius: "4px",
					backgroundColor: "var(--vscode-inputValidation-errorBackground)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
						<AlertCircle className="w-4 h-4 text-red-500" />
						<span style={{ fontWeight: "500", color: "var(--vscode-errorForeground)" }}>
							Scan Errors ({scanErrors.length})
						</span>
					</div>
					{scanErrors.slice(0, 3).map((error, index) => (
						<div key={index} style={{ fontSize: "14px", color: "var(--vscode-errorForeground)", marginBottom: "4px" }}>
							{error}
						</div>
					))}
					{scanErrors.length > 3 && (
						<div style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)" }}>
							... and {scanErrors.length - 3} more errors
						</div>
					)}
				</div>
			)}

			<VSCodeDivider />

			{/* Help Text */}
			<div style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)", lineHeight: "1.4" }}>
				<p style={{ margin: "0 0 8px 0" }}>
					The AST database indexes your codebase to provide enhanced autocomplete suggestions based on your project's structure and symbols.
				</p>
				<p style={{ margin: "0" }}>
					Scanning may take a few minutes for large projects. The database will be automatically updated when you make changes to your code.
				</p>
			</div>
		</div>
	)
}

export default memo(CodebaseSettingsSection)
