# FIM 连接稳定性修复

## 问题分析

错误 "terminated" 通常表示流式连接被意外中断，可能的原因：

1. **服务器端主动关闭连接**
2. **网络连接不稳定**
3. **代理或防火墙干扰**
4. **服务器处理超时**
5. **流式响应格式问题**

## 修复措施

### 1. **重试机制**
```typescript
async *completePromptStream(prompt: string, suffix?: string): AsyncGenerator<string> {
  const maxRetries = 2
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      yield* this.completePromptStreamInternal(prompt, suffix)
      return // 成功，退出重试循环
    } catch (error) {
      // 指数退避重试：1s, 2s, 4s...
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
}
```

### 2. **改进的流式处理**
```typescript
// 添加缓冲区处理不完整的行
let buffer = ""
const lines = buffer.split('\n')
buffer = lines.pop() || "" // 保留最后一个不完整的行
```

### 3. **更好的请求头**
```typescript
const headers = {
  "Content-Type": "application/json",
  "Authorization": `Bearer ${apiKey}`,
  "Accept": "text/event-stream",        // 明确接受 SSE
  "Cache-Control": "no-cache",          // 禁用缓存
  "Connection": "keep-alive",           // 保持连接
}
```

### 4. **详细的错误处理**
```typescript
if (error.message.includes('terminated') || error.message.includes('aborted')) {
  throw new Error(`FIM API streaming connection terminated:
URL: ${url}
Error: ${error.message}
Possible causes: Server closed connection, network instability, or proxy interference
Suggestion: Check server logs, network connectivity, or try reducing request frequency`)
}
```

### 5. **连接健康检查**
```typescript
async testConnection(): Promise<boolean> {
  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify({
        prompt: "test",
        suffix: "",
        stream: false,
        max_tokens: 1,
        temperature: 0.1,
      }),
      signal: AbortSignal.timeout(5000), // 5秒超时
    })
    
    return response.ok || response.status === 400
  } catch (error) {
    return false
  }
}
```

### 6. **详细的调试日志**
```typescript
console.log('FIM API streaming attempt 1/3')
console.log('FIM API received chunk:', chunk)
console.log('FIM API stream completed normally')
console.warn('FIM API streaming attempt 1 failed:', error)
```

## 调试建议

### 检查服务器日志
1. 查看 `kubemlsvc.qianxin-inc.cn` 的服务器日志
2. 确认是否有连接超时或资源限制
3. 检查是否有请求频率限制

### 网络诊断
```bash
# 测试连接
curl -v http://kubemlsvc.qianxin-inc.cn/svc-d70rsdpypzxs/v1/completions

# 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

### 配置调整建议

1. **增加超时时间**：
```typescript
requestTimeoutMs: 60000 // 增加到60秒
```

2. **减少并发请求**：
确保同时只有一个 FIM 请求在进行

3. **检查防火墙设置**：
确保企业防火墙不会中断长连接

## 预期改进

### 重试机制
- 自动重试最多3次
- 指数退避延迟（1s, 2s, 4s）
- 不重试超时错误

### 连接稳定性
- 正确的 SSE 请求头
- 缓冲区处理不完整数据
- 详细的连接状态日志

### 错误诊断
- 明确的错误分类
- 详细的调试信息
- 具体的解决建议

## 使用建议

1. **监控日志**：观察重试次数和成功率
2. **网络检查**：确认到服务器的连接稳定性
3. **配置优化**：根据网络环境调整超时时间
4. **错误报告**：收集详细的错误信息用于进一步诊断

现在 FIM API 应该能更好地处理连接中断问题，并提供详细的诊断信息！
