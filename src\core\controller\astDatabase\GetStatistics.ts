/**
 * Get Statistics Controller
 */

import { Controller } from ".."
import { GetStatisticsResponse, DatabaseStatistics } from "@shared/proto/astdb"
import type { GetStatisticsRequest } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function GetStatistics(controller: Controller, request: GetStatisticsRequest): Promise<GetStatisticsResponse> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.getStatistics(request)

		return response
	} catch (error) {
		console.error("Failed to get statistics:", error)

		// Return empty response if service is not available
		return GetStatisticsResponse.create({
			statistics: DatabaseStatistics.create({
				totalFiles: 0,
				totalDefinitions: 0,
				totalUsages: 0,
				definitionsByType: {},
				filesWithMostDefinitions: [],
			}),
		})
	}
}
