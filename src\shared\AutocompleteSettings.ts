/**
 * Autocomplete provider types
 */
export type AutocompleteProvider = "openai" | "fim"

/**
 * Autocomplete configuration settings for QAX Complete
 */
export interface AutocompleteSettings {
	/**
	 * Whether autocomplete is enabled
	 */
	enabled: boolean

	/**
	 * Provider type for autocomplete service
	 */
	provider?: AutocompleteProvider

	/**
	 * API key for the autocomplete service
	 */
	apiKey?: string

	/**
	 * Base URL for the autocomplete API
	 */
	apiBaseUrl?: string

	/**
	 * Model ID to use for autocomplete
	 */
	modelId?: string

	/**
	 * Maximum number of tokens for completion
	 */
	maxTokens?: number

	/**
	 * Temperature for completion generation
	 */
	temperature?: number

	/**
	 * Request timeout in milliseconds
	 */
	requestTimeoutMs?: number

	/**
	 * Whether to use prompt caching
	 */
	usePromptCache?: boolean

	/**
	 * Custom headers for API requests
	 */
	customHeaders?: Record<string, string>

	/**
	 * Debounce time in milliseconds for autocomplete requests
	 */
	debounceMs?: number

	/**
	 * Whether to use AST database for enhanced context
	 */
	useAstDatabase?: boolean

	/**
	 * Timeout for AST database operations in milliseconds
	 */
	astDatabaseTimeout?: number

	/**
	 * Maximum number of definitions to fetch from AST database
	 */
	astDatabaseMaxDefinitions?: number

	/**
	 * FIM-specific settings
	 */
	fim?: {
		/**
		 * API key for FIM service
		 */
		apiKey?: string

		/**
		 * Base URL for FIM API
		 */
		baseUrl?: string
	}
}

/**
 * Default autocomplete settings
 */
export const DEFAULT_AUTOCOMPLETE_SETTINGS: AutocompleteSettings = {
	enabled: false,
	provider: "openai",
	apiBaseUrl: "https://api.openrouter.ai/api/v1", // Default to OpenRouter, but supports any OpenAI-compatible API
	modelId: "google/gemini-2.5-flash-preview-05-20",
	maxTokens: 1000,
	temperature: 0.1,
	requestTimeoutMs: 30000,
	usePromptCache: false,
	customHeaders: {},
	debounceMs: 300, // 300ms debounce by default
	useAstDatabase: false, // AST database disabled by default
	astDatabaseTimeout: 2000, // 2 second timeout for AST operations
	astDatabaseMaxDefinitions: 10, // Maximum 10 definitions from AST database
	fim: {
		baseUrl: "",
		apiKey: "",
	},
}

/**
 * Validates autocomplete settings
 */
export function validateAutocompleteSettings(settings: Partial<AutocompleteSettings>): string[] {
	const errors: string[] = []

	if (settings.enabled) {
		if (settings.provider === "fim") {
			// For FIM provider, check FIM-specific settings
			if (!settings.fim?.apiKey) {
				errors.push("FIM API key is required when FIM provider is enabled")
			}
			if (!settings.fim?.baseUrl) {
				errors.push("FIM base URL is required when FIM provider is enabled")
			} else if (!isValidUrl(settings.fim.baseUrl)) {
				errors.push("Invalid FIM base URL")
			}
		} else {
			// For OpenAI provider, check general API key
			if (!settings.apiKey) {
				errors.push("API key is required when autocomplete is enabled")
			}
		}
	}

	if (settings.apiBaseUrl && !isValidUrl(settings.apiBaseUrl)) {
		errors.push("Invalid API base URL")
	}

	if (settings.maxTokens && (settings.maxTokens < 1 || settings.maxTokens > 10000)) {
		errors.push("Max tokens must be between 1 and 10000")
	}

	if (settings.temperature && (settings.temperature < 0 || settings.temperature > 2)) {
		errors.push("Temperature must be between 0 and 2")
	}

	if (settings.requestTimeoutMs && (settings.requestTimeoutMs < 1000 || settings.requestTimeoutMs > 300000)) {
		errors.push("Request timeout must be between 1000ms and 300000ms")
	}

	if (settings.debounceMs && (settings.debounceMs < 0 || settings.debounceMs > 5000)) {
		errors.push("Debounce time must be between 0ms and 5000ms")
	}

	return errors
}

/**
 * Helper function to validate URL
 */
function isValidUrl(url: string): boolean {
	try {
		new URL(url)
		return true
	} catch {
		return false
	}
}
