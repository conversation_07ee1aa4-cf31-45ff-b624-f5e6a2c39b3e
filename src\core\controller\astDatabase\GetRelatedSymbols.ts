/**
 * Get Related Symbols Controller
 */

import { Controller } from ".."
import { GetRelatedSymbolsResponse } from "@shared/proto/astdb"
import type { GetRelatedSymbolsRequest } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function GetRelatedSymbols(
	controller: Controller,
	request: GetRelatedSymbolsRequest,
): Promise<GetRelatedSymbolsResponse> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.getRelatedSymbols(request)

		return response
	} catch (error) {
		console.error("Failed to get related symbols:", error)

		// Return empty response if service is not available
		return GetRelatedSymbolsResponse.create({
			symbols: [],
		})
	}
}
