/**
 * Example usage and testing of the AST-RAG implementation
 */

import * as path from "path"
import {
	AstDB,
	WorkspaceScanner,
	scanWorkspaceForAST,
	retrieveAstBasedExtraContext,
	SimpleTokenizer,
	CursorPosition,
	PostprocessSettings,
	AstDefinition,
	SymbolType,
	AstDefinitionHelper,
	formatScanProgress,
	createProgressReport,
	createLogger,
	LogLevel,
} from "./index"

/**
 * Comprehensive demo of AST database functionality
 */
async function demonstrateAstRag() {
	console.log("=== AST Database Demo ===\n")

	const logger = createLogger("Demo", LogLevel.INFO)

	// Initialize AST database
	console.log("1. Initializing AST database...")
	const astDb = new AstDB("./demo-ast.json")

	try {
		// Create some sample AST definitions
		console.log("2. Adding sample definitions to database...")

		const sampleDefinitions: AstDefinition[] = [
			{
				officialPath: ["example.ts", "Calculator", "add"],
				symbolType: SymbolType.Method,
				usages: [
					{
						targetsForGuesswork: ["Calculator::add"],
						resolvedAs: "example.ts::Calculator::add",
						debugHint: "method call",
						uline: 15,
					},
				],
				resolvedType: "function",
				thisIsAClass: "Calculator",
				thisClassDerivedFrom: [],
				cpath: path.resolve("./example.ts"),
				declLine1: 5,
				declLine2: 5,
				bodyLine1: 5,
				bodyLine2: 10,
			},
			{
				officialPath: ["utils.ts", "Helper", "formatNumber"],
				symbolType: SymbolType.Function,
				usages: [
					{
						targetsForGuesswork: ["Helper::formatNumber"],
						resolvedAs: "utils.ts::Helper::formatNumber",
						debugHint: "function call",
						uline: 20,
					},
				],
				resolvedType: "function",
				thisIsAClass: "",
				thisClassDerivedFrom: [],
				cpath: path.resolve("./utils.ts"),
				declLine1: 12,
				declLine2: 12,
				bodyLine1: 12,
				bodyLine2: 18,
			},
		]

		// Store definitions in database
		for (const def of sampleDefinitions) {
			const id = await astDb.storeDefinition(def)
			console.log(`   Stored definition: ${AstDefinitionHelper.path(def)} (ID: ${id})`)
		}

		// Test search functionality
		console.log("\n3. Testing search functionality...")
		const searchResults = await astDb.searchDefinitions("Calculator")
		console.log(`Found ${searchResults.length} definitions matching "Calculator":`)
		for (const def of searchResults) {
			console.log(`   - ${AstDefinitionHelper.path(def)} (${def.symbolType})`)
		}

		// Test statistics
		console.log("\n4. Database statistics:")
		const stats = astDb.getStatistics()
		console.log(`   Total definitions: ${stats.totalDefinitions}`)
		console.log(`   Total usages: ${stats.totalUsages}`)
		console.log(`   Total files: ${stats.totalFiles}`)
		console.log(`   Definitions by type:`, stats.definitionsByType)

		// Test detailed status
		console.log("\n5. Detailed status:")
		const detailedStatus = astDb.getDetailedStatus()
		console.log(`   State: ${detailedStatus.astate}`)
		console.log(`   Database size: ${(detailedStatus.dbSizeBytes / 1024).toFixed(1)} KB`)
		console.log(`   Average definitions per file: ${detailedStatus.averageDefinitionsPerFile.toFixed(1)}`)

		// Test context retrieval
		console.log("\n6. Testing context retrieval...")
		const tokenizer = new SimpleTokenizer("starcoder", 0.5)
		const cursorPos: CursorPosition = {
			file: path.resolve("./example.ts"),
			line: 15,
			character: 10,
		}

		const ppSettings: PostprocessSettings = {
			maxFilesN: 3,
			maxTokensPerFile: 200,
		}

		const contextUsed: Record<string, any> = {}
		const extraContext = await retrieveAstBasedExtraContext(
			astDb,
			tokenizer,
			cursorPos.file,
			cursorPos,
			[Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER],
			ppSettings,
			500,
			contextUsed,
			[process.cwd()],
		)

		console.log("Generated context:")
		console.log(extraContext || "(empty context)")
	} catch (error) {
		logger.error("Error during demo", error as Error)
		console.error("Error during demo:", error)
	} finally {
		// Clean up
		console.log("\n7. Cleaning up...")
		await astDb.close()
		console.log("Demo completed!")
	}
}

/**
 * Demo workspace scanning functionality
 */
async function demonstrateWorkspaceScanning() {
	console.log("\n=== Workspace Scanning Demo ===\n")

	const logger = createLogger("WorkspaceDemo", LogLevel.INFO)

	try {
		console.log("1. Scanning current workspace...")

		const { astDb, scanner } = await scanWorkspaceForAST(process.cwd(), {
			maxFiles: 50,
			includeExtensions: ["ts", "js"],
			excludePatterns: ["node_modules", ".git", "dist"],
			onProgress: (progress: any) => {
				const report = createProgressReport(progress)
				console.log(`   ${report.summary}`)

				if (progress.status === "complete") {
					console.log(`   Performance: ${report.details.performance}`)
					console.log(`   Results: ${report.details.results}`)
					if (report.details.errors.length > 0) {
						console.log(`   Errors: ${report.details.errors.slice(0, 3).join(", ")}`)
					}
				}
			},
		})

		console.log("\n2. Workspace scan results:")
		const stats = astDb.getStatistics()
		console.log(`   Files processed: ${stats.totalFiles}`)
		console.log(`   Definitions found: ${stats.totalDefinitions}`)
		console.log(`   Usages found: ${stats.totalUsages}`)
		console.log(`   Definition types:`, stats.definitionsByType)

		console.log("\n3. Top files by definition count:")
		for (const { file, count } of stats.filesWithMostDefinitions.slice(0, 5)) {
			console.log(`   ${path.relative(process.cwd(), file)}: ${count} definitions`)
		}

		await astDb.close()
	} catch (error) {
		logger.error("Error during workspace scanning demo", error as Error)
		console.error("Error during workspace scanning demo:", error)
	}
}

/**
 * Run all demos
 */
async function runAllDemos() {
	await demonstrateAstRag()
	await demonstrateWorkspaceScanning()
}

// Run the demo
if (require.main === module) {
	runAllDemos().catch(console.error)
}

export { demonstrateAstRag, demonstrateWorkspaceScanning, runAllDemos }
