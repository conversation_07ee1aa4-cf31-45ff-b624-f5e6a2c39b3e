/**
 * Logger utility for AST database operations
 * Provides structured logging with different levels and contexts
 */

export enum LogLevel {
	DEBUG = 0,
	INFO = 1,
	WARN = 2,
	ERROR = 3,
}

export interface LogEntry {
	timestamp: Date
	level: LogLevel
	message: string
	context?: string
	data?: any
	error?: Error
}

export class AstLogger {
	private logs: LogEntry[] = []
	private maxLogs: number
	private minLevel: LogLevel
	private context: string

	constructor(context: string = "AstDB", minLevel: LogLevel = LogLevel.INFO, maxLogs: number = 1000) {
		this.context = context
		this.minLevel = minLevel
		this.maxLogs = maxLogs
	}

	/**
	 * Log a debug message
	 */
	debug(message: string, data?: any): void {
		this.log(LogLevel.DEBUG, message, data)
	}

	/**
	 * Log an info message
	 */
	info(message: string, data?: any): void {
		this.log(LogLevel.INFO, message, data)
	}

	/**
	 * Log a warning message
	 */
	warn(message: string, data?: any): void {
		this.log(LogLevel.WARN, message, data)
	}

	/**
	 * Log an error message
	 */
	error(message: string, error?: Error, data?: any): void {
		this.log(LogLevel.ERROR, message, data, error)
	}

	/**
	 * Log a message with specified level
	 */
	private log(level: LogLevel, message: string, data?: any, error?: Error): void {
		if (level < this.minLevel) {
			return
		}

		const entry: LogEntry = {
			timestamp: new Date(),
			level,
			message,
			context: this.context,
			data,
			error,
		}

		this.logs.push(entry)

		// Trim logs if we exceed max
		if (this.logs.length > this.maxLogs) {
			this.logs = this.logs.slice(-this.maxLogs)
		}

		// Also log to console for development
		this.logToConsole(entry)
	}

	/**
	 * Log to console with appropriate method
	 */
	private logToConsole(entry: LogEntry): void {
		const levelName = LogLevel[entry.level]
		const timestamp = entry.timestamp.toISOString()
		const prefix = `[${timestamp}] [${entry.context}] [${levelName}]`
		const message = `${prefix} ${entry.message}`

		switch (entry.level) {
			case LogLevel.DEBUG:
				console.debug(message, entry.data)
				break
			case LogLevel.INFO:
				console.info(message, entry.data)
				break
			case LogLevel.WARN:
				console.warn(message, entry.data)
				break
			case LogLevel.ERROR:
				console.error(message, entry.error || entry.data)
				break
		}
	}

	/**
	 * Get all log entries
	 */
	getLogs(): LogEntry[] {
		return [...this.logs]
	}

	/**
	 * Get logs filtered by level
	 */
	getLogsByLevel(level: LogLevel): LogEntry[] {
		return this.logs.filter((log) => log.level === level)
	}

	/**
	 * Get recent logs
	 */
	getRecentLogs(count: number = 50): LogEntry[] {
		return this.logs.slice(-count)
	}

	/**
	 * Clear all logs
	 */
	clearLogs(): void {
		this.logs = []
	}

	/**
	 * Get log statistics
	 */
	getLogStats(): {
		total: number
		byLevel: Record<string, number>
		oldestEntry?: Date
		newestEntry?: Date
	} {
		const byLevel: Record<string, number> = {}

		for (const log of this.logs) {
			const levelName = LogLevel[log.level]
			byLevel[levelName] = (byLevel[levelName] || 0) + 1
		}

		return {
			total: this.logs.length,
			byLevel,
			oldestEntry: this.logs.length > 0 ? this.logs[0].timestamp : undefined,
			newestEntry: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : undefined,
		}
	}

	/**
	 * Create a child logger with additional context
	 */
	child(additionalContext: string): AstLogger {
		const childContext = `${this.context}:${additionalContext}`
		const child = new AstLogger(childContext, this.minLevel, this.maxLogs)
		child.logs = this.logs // Share log storage
		return child
	}

	/**
	 * Set minimum log level
	 */
	setMinLevel(level: LogLevel): void {
		this.minLevel = level
	}

	/**
	 * Export logs as JSON
	 */
	exportLogs(): string {
		return JSON.stringify(this.logs, null, 2)
	}
}

/**
 * Default logger instance
 */
export const defaultLogger = new AstLogger("AstDB")

/**
 * Create a logger for a specific component
 */
export function createLogger(context: string, minLevel: LogLevel = LogLevel.INFO): AstLogger {
	return new AstLogger(context, minLevel)
}

/**
 * Error handling utilities
 */
export class AstError extends Error {
	public readonly code: string
	public readonly context?: string
	public readonly data?: any

	constructor(message: string, code: string = "AST_ERROR", context?: string, data?: any) {
		super(message)
		this.name = "AstError"
		this.code = code
		this.context = context
		this.data = data
	}
}

/**
 * Wrap async operations with error handling and logging
 */
export async function withErrorHandling<T>(
	operation: () => Promise<T>,
	logger: AstLogger,
	operationName: string,
	context?: string,
): Promise<T> {
	try {
		logger.debug(`Starting ${operationName}`, { context })
		const result = await operation()
		logger.debug(`Completed ${operationName}`, { context })
		return result
	} catch (error) {
		const astError =
			error instanceof AstError
				? error
				: new AstError(`Failed to ${operationName}: ${error}`, "OPERATION_FAILED", context, { originalError: error })
		logger.error(`Error in ${operationName}`, astError, { context })
		throw astError
	}
}

/**
 * Retry an operation with exponential backoff
 */
export async function withRetry<T>(
	operation: () => Promise<T>,
	logger: AstLogger,
	operationName: string,
	maxRetries: number = 3,
	baseDelayMs: number = 1000,
): Promise<T> {
	let lastError: Error | undefined

	for (let attempt = 1; attempt <= maxRetries; attempt++) {
		try {
			return await operation()
		} catch (error) {
			lastError = error as Error

			if (attempt === maxRetries) {
				logger.error(`Final attempt failed for ${operationName}`, lastError)
				throw lastError
			}

			const delayMs = baseDelayMs * Math.pow(2, attempt - 1)
			logger.warn(`Attempt ${attempt} failed for ${operationName}, retrying in ${delayMs}ms`, lastError)

			await new Promise((resolve) => setTimeout(resolve, delayMs))
		}
	}

	throw lastError
}
