# FIM 上下文限制实现

## 问题背景

为了避免 FIM API 请求过大导致的性能问题和连接中断，需要限制发送的上下文大小。

## 实现的限制策略

### 1. **行数限制（AutocompleteProvider 层面）**

```typescript
// 限制总行数为 100 行
const maxLines = 100
const currentLine = position.line

// 70% 用于光标前的上下文，30% 用于光标后的上下文
const linesBeforeLimit = Math.min(currentLine, Math.floor(maxLines * 0.7))
const linesAfterLimit = Math.min(document.lineCount - currentLine - 1, maxLines - linesBeforeLimit)

// 计算实际的起始和结束行
const startLine = Math.max(0, currentLine - linesBeforeLimit)
const endLine = Math.min(document.lineCount, currentLine + linesAfterLimit + 1)
```

**特点**：
- 总共最多 100 行代码
- 光标前最多 70 行（保留更多历史上下文）
- 光标后最多 30 行（适量的后续上下文）
- 动态调整：如果前面行数不足，会给后面分配更多行数

### 2. **字符数限制（FIM Handler 层面）**

```typescript
// 限制总字符数为 8000 字符
const maxContextLength = 8000
const limitedPrompt = this.limitContextSize(prompt, maxContextLength * 0.7)  // 5600 字符
const limitedSuffix = this.limitContextSize(suffix, maxContextLength * 0.3)  // 2400 字符
```

**特点**：
- 总共最多 8000 字符
- Prompt（光标前）最多 5600 字符
- Suffix（光标后）最多 2400 字符
- 智能截取：保留最相关的上下文

### 3. **智能截取算法**

```typescript
private limitContextSize(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text
  }
  
  const lines = text.split('\n')
  
  // 对于 prompt（光标前），保留末尾（最近的上下文）
  if (!text.startsWith(' ') && !text.startsWith('\t')) {
    // 从末尾开始保留行
    for (let i = lines.length - 1; i >= 0; i--) {
      // 逐行添加直到达到长度限制
    }
  } else {
    // 对于 suffix（光标后），保留开头
    for (const line of lines) {
      // 从开头逐行添加直到达到长度限制
    }
  }
}
```

## 限制效果

### 请求大小控制
- **行数限制**: 最多 100 行代码
- **字符限制**: 最多 8000 字符
- **双重保护**: 两层限制确保请求不会过大

### 上下文质量
- **智能分配**: 70% 给历史上下文，30% 给后续上下文
- **相关性优先**: 保留最接近光标的代码
- **完整性保护**: 按行截取，避免截断代码结构

### 性能优化
- **减少网络传输**: 更小的请求体积
- **降低服务器负载**: 减少处理的文本量
- **提高响应速度**: 更快的处理和传输

## 调试信息

### AutocompleteProvider 日志
```
🚀📏 FIM context: 45 lines before, 30 lines after (total: 75 lines)
```

### FIM Handler 日志
```
🚀📏 FIM context sizes - prompt: 3200 chars, suffix: 1800 chars
🚀📏 FIM streaming context sizes - prompt: 3200 chars, suffix: 1800 chars
```

## 配置建议

### 可调整的参数

1. **最大行数** (AutocompleteProvider):
```typescript
const maxLines = 100  // 可根据需要调整
```

2. **最大字符数** (FIM Handler):
```typescript
const maxContextLength = 8000  // 可根据 API 限制调整
```

3. **分配比例**:
```typescript
const beforeRatio = 0.7  // 70% 给光标前
const afterRatio = 0.3   // 30% 给光标后
```

### 根据文件类型优化

可以考虑根据不同文件类型调整限制：
- **大型文件**: 更严格的限制
- **配置文件**: 可以适当放宽
- **脚本文件**: 重点保留函数定义

## 预期效果

1. **连接稳定性提升**: 减少因请求过大导致的连接中断
2. **响应速度提升**: 更小的上下文处理更快
3. **服务器负载降低**: 减少计算资源消耗
4. **用户体验改善**: 更稳定的代码补全功能

现在 FIM API 请求将被限制在合理的大小范围内，应该能显著改善连接稳定性！
