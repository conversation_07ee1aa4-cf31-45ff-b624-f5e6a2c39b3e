syntax = "proto3";
package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

import "common.proto";

service StateService {
  rpc getLatestState(EmptyRequest) returns (State);
  rpc updateTerminalConnectionTimeout(Int64Request) returns (Int64);
  rpc updateTerminalReuseEnabled(BooleanRequest) returns (Empty);
  rpc updateDefaultTerminalProfile(StringRequest) returns (TerminalProfileUpdateResponse);
  rpc getAvailableTerminalProfiles(EmptyRequest) returns (TerminalProfiles);
  rpc subscribeToState(EmptyRequest) returns (stream State);
  rpc toggleFavoriteModel(StringRequest) returns (Empty);
  rpc resetState(ResetStateRequest) returns (Empty);
  rpc togglePlanActMode(TogglePlanActModeRequest) returns (Boolean);
  rpc updateAutoApprovalSettings(AutoApprovalSettingsRequest) returns (Empty);
  rpc updateSettings(UpdateSettingsRequest) returns (Empty);
  rpc updateTelemetrySetting(TelemetrySettingRequest) returns (Empty);
}

message State {
  string state_json = 1;
}

message TerminalProfiles {
  repeated TerminalProfile profiles = 1;
}

message TerminalProfile {
  string id = 1;
  string name = 2;
  optional string path = 3;
  optional string description = 4;
}

message TerminalProfileUpdateResponse {
  int32 closed_count = 1;
  int32 busy_terminals_count = 2;
  bool has_busy_terminals = 3;
}

message TogglePlanActModeRequest {
  Metadata metadata = 1;
  ChatSettings chat_settings = 2;
  optional ChatContent chat_content = 3;
}

enum PlanActMode {
  PLAN = 0;
  ACT = 1;
}

message ChatSettings {
  PlanActMode mode = 1;
  optional string preferred_language = 2;
  optional string open_ai_reasoning_effort = 3;
}

message ChatContent {
  optional string message = 1;
  repeated string images = 2;
  repeated string files = 3;
}

message ResetStateRequest {
  Metadata metadata = 1;
  optional bool global = 2;
}

message AutoApprovalSettingsRequest {
  Metadata metadata = 1;
  message Actions {
    bool read_files = 1;
    bool read_files_externally = 2;
    bool edit_files = 3;
    bool edit_files_externally = 4;
    bool execute_safe_commands = 5;
    bool execute_all_commands = 6;
    bool use_browser = 7;
    bool use_mcp = 8;
  }
  int32 version = 2;
  bool enabled = 3;
  Actions actions = 4;
  int32 max_requests = 5;
  bool enable_notifications = 6;
  repeated string favorites = 7;
}

enum TelemetrySettingEnum {
  UNSET = 0;
  ENABLED = 1;
  DISABLED = 2;
}

message TelemetrySettingRequest {
  Metadata metadata = 1;
  TelemetrySettingEnum setting = 2;
}

// Message for updating settings
message UpdateSettingsRequest {
  Metadata metadata = 1;
  optional ApiConfiguration api_configuration = 2;
  optional string telemetry_setting = 3;
  optional bool plan_act_separate_models_setting = 4;
  optional bool enable_checkpoints_setting = 5;
  optional bool mcp_marketplace_enabled = 6;
  optional ChatSettings chat_settings = 7;
  optional int64 shell_integration_timeout = 8;
  optional bool terminal_reuse_enabled = 9;
  optional bool mcp_responses_collapsed = 10;
  optional bool mcp_rich_display_enabled = 11;
  optional int64 terminal_output_line_limit = 12;
}

// Complete API Configuration message
message ApiConfiguration {
  // Core API fields
  optional string api_provider = 1;
  optional string api_model_id = 2;
  optional string api_key = 3; // anthropic
  optional string api_base_url = 4;
  
  // Provider-specific API keys
  optional string cline_api_key = 5;
  optional string openrouter_api_key = 6;
  optional string anthropic_base_url = 7;
  optional string openai_api_key = 8;
  optional string openai_native_api_key = 9;
  optional string gemini_api_key = 10;
  optional string deepseek_api_key = 11;
  optional string requesty_api_key = 12;
  optional string together_api_key = 13;
  optional string fireworks_api_key = 14;
  optional string qwen_api_key = 15;
  optional string doubao_api_key = 16;
  optional string mistral_api_key = 17;
  optional string nebius_api_key = 18;
  optional string asksage_api_key = 19;
  optional string xai_api_key = 20;
  optional string sambanova_api_key = 21;
  optional string cerebras_api_key = 22;
  optional string qax_api_key = 23;
  
  // Model IDs
  optional string openrouter_model_id = 24;
  optional string openai_model_id = 25;
  optional string anthropic_model_id = 26;
  optional string bedrock_model_id = 27;
  optional string vertex_model_id = 28;
  optional string gemini_model_id = 29;
  optional string ollama_model_id = 30;
  optional string lm_studio_model_id = 31;
  optional string litellm_model_id = 32;
  optional string requesty_model_id = 33;
  optional string together_model_id = 34;
  optional string fireworks_model_id = 35;
  optional string qax_model_id = 36;
  
  // AWS Bedrock fields
  optional bool aws_bedrock_custom_selected = 37;
  optional string aws_bedrock_custom_model_base_id = 38;
  optional string aws_access_key = 39;
  optional string aws_secret_key = 40;
  optional string aws_session_token = 41;
  optional string aws_region = 42;
  optional bool aws_use_cross_region_inference = 43;
  optional bool aws_bedrock_use_prompt_cache = 44;
  optional bool aws_use_profile = 45;
  optional string aws_profile = 46;
  optional string aws_bedrock_endpoint = 47;
  
  // Vertex AI fields
  optional string vertex_project_id = 48;
  optional string vertex_region = 49;
  
  // Base URLs and endpoints
  optional string openai_base_url = 50;
  optional string ollama_base_url = 51;
  optional string lm_studio_base_url = 52;
  optional string gemini_base_url = 53;
  optional string litellm_base_url = 54;
  optional string asksage_api_url = 55;
  optional string qax_base_url = 56;
  
  // LiteLLM specific fields
  optional string litellm_api_key = 57;
  optional bool litellm_use_prompt_cache = 58;
  
  // Model configuration
  optional int64 thinking_budget_tokens = 59;
  optional string reasoning_effort = 60;
  optional int64 request_timeout_ms = 61;
  
  // Fireworks specific
  optional int64 fireworks_model_max_completion_tokens = 62;
  optional int64 fireworks_model_max_tokens = 63;
  
  // Azure specific
  optional string azure_api_version = 64;
  
  // Ollama specific
  optional string ollama_api_options_ctx_num = 65;
  
  // Qwen specific
  optional string qwen_api_line = 66;
  
  // OpenRouter specific
  optional string openrouter_provider_sorting = 67;
  
  // VSCode LM (stored as JSON string due to complex type)
  optional string vscode_lm_model_selector = 68;
  
  // Model info objects (stored as JSON strings)
  optional string openrouter_model_info = 69;
  optional string openai_model_info = 70;
  optional string requesty_model_info = 71;
  optional string litellm_model_info = 72;
  
  // OpenAI headers (stored as JSON string)
  optional string openai_headers = 73;
  
  // Favorited model IDs
  repeated string favorited_model_ids = 74;

  // SAP AI Core specific
  optional string sap_ai_core_client_id = 75;
  optional string sap_ai_core_client_secret = 76;
  optional string sap_ai_core_base_url = 77;
  optional string sap_ai_core_token_url = 78;
  optional string sap_ai_resource_group = 79;

  // Claude Code specific
  optional string claude_code_path = 80;
}
