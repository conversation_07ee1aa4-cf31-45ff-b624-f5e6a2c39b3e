# AST Database Module

基于抽象语法树的数据库模块，为 autocomplete 和代码检索提供高效的上下文检索能力。

## 功能特性

- **AST 解析**: 集成 tree-sitter 解析服务，支持多种编程语言
- **数据库存储**: 使用内存数据库和文件持久化存储 AST 定义和使用信息
- **工作区扫描**: 自动扫描整个工作区的代码文件并建立 AST 索引
- **上下文检索**: 基于 AST 分析提供智能的代码上下文检索
- **进度跟踪**: 详细的扫描进度和状态报告
- **错误处理**: 完善的错误处理和日志记录机制

## 支持的语言

- JavaScript/TypeScript (.js, .jsx, .ts, .tsx)
- Python (.py)
- Rust (.rs)
- Go (.go)
- C/C++ (.c, .h, .cpp, .hpp)
- C# (.cs)
- Ruby (.rb)
- Java (.java)
- PHP (.php)
- Swift (.swift)
- Kotlin (.kt)

## 基本使用

### 1. 初始化 AST 数据库

```typescript
import { AstDB } from '@services/astdb'

// 创建数据库实例
const astDb = new AstDB('./my-project-ast.json')

// 获取状态
const status = astDb.getStatus()
console.log('Database status:', status.astate)
```

### 2. 扫描工作区

```typescript
import { scanWorkspaceForAST, formatScanProgress } from '@services/astdb'

// 扫描当前工作区
const { astDb, scanner } = await scanWorkspaceForAST(process.cwd(), {
  maxFiles: 1000,
  includeExtensions: ['ts', 'js', 'py'],
  excludePatterns: ['node_modules', '.git', 'dist'],
  onProgress: (progress) => {
    console.log(formatScanProgress(progress))
  }
})

// 获取扫描结果
const stats = astDb.getStatistics()
console.log(`Found ${stats.totalDefinitions} definitions in ${stats.totalFiles} files`)
```

### 3. 查询定义

```typescript
// 按文件路径查询
const definitions = await astDb.getDefinitionsByPath('/path/to/file.ts')

// 搜索定义
const searchResults = await astDb.searchDefinitions('MyClass')

// 按类型查询
const functions = await astDb.getDefinitionsByType('function')
```

### 4. 获取代码上下文

```typescript
import { retrieveAstBasedExtraContext, SimpleTokenizer } from '@services/astdb'

const tokenizer = new SimpleTokenizer('starcoder', 0.5)
const cursorPos = {
  file: '/path/to/file.ts',
  line: 42,
  character: 10
}

const context = await retrieveAstBasedExtraContext(
  astDb,
  tokenizer,
  cursorPos.file,
  cursorPos,
  [Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER], // 无忽略范围
  { maxFilesN: 5 }, // 后处理设置
  1000, // RAG token 预算
  {}, // 上下文使用跟踪
  [process.cwd()] // 项目目录
)

console.log('Generated context:', context)
```

## 高级功能

### 日志记录

```typescript
import { createLogger, LogLevel } from '@services/astdb'

const logger = createLogger('MyComponent', LogLevel.DEBUG)
logger.info('Starting operation')
logger.error('Operation failed', error)
```

### 错误处理

```typescript
import { withErrorHandling, AstErrorClass } from '@services/astdb'

try {
  await withErrorHandling(
    async () => {
      // 你的操作
    },
    logger,
    'my-operation',
    'context-info'
  )
} catch (error) {
  if (error instanceof AstErrorClass) {
    console.log('AST Error:', error.code, error.context)
  }
}
```

### 自定义扫描选项

```typescript
import { WorkspaceScanner } from '@services/astdb'

const astDb = new AstDB()
const scanner = new WorkspaceScanner(astDb)

await scanner.scanWorkspace('/path/to/project', {
  maxFiles: 500,
  includeExtensions: ['ts', 'js'],
  excludePatterns: ['node_modules', 'test'],
  onProgress: (progress) => {
    if (progress.status === 'parsing') {
      console.log(`Processing: ${progress.currentFile}`)
    }
  }
})
```

## 示例和测试

### 运行示例

```typescript
import { runAllDemos } from '@services/astdb'

// 运行所有演示
await runAllDemos()
```

### 运行测试

```typescript
import { runTests } from '@services/astdb'

// 运行测试套件
const success = await runTests()
if (success) {
  console.log('All tests passed!')
} else {
  console.log('Some tests failed')
}
```

## API 参考

### AstDB 类

- `storeDefinition(definition: AstDefinition): Promise<number>` - 存储定义
- `getDefinitionsByPath(path: string): Promise<AstDefinition[]>` - 按路径获取定义
- `searchDefinitions(symbolName: string, limit?: number): Promise<AstDefinition[]>` - 搜索定义
- `getStatistics()` - 获取数据库统计信息
- `getDetailedStatus()` - 获取详细状态
- `clearFile(path: string): Promise<void>` - 清除文件数据
- `close(): Promise<void>` - 关闭数据库

### WorkspaceScanner 类

- `scanWorkspace(path: string, options?: ScanOptions): Promise<void>` - 扫描工作区
- `getStatus(): AstStatus` - 获取扫描状态

### 工具函数

- `scanWorkspaceForAST(path: string, options?: ScanOptions)` - 便捷的工作区扫描函数
- `formatScanProgress(progress: ScanProgress): string` - 格式化进度信息
- `createProgressReport(progress: ScanProgress)` - 创建详细进度报告

## 配置选项

### ScanOptions

```typescript
interface ScanOptions {
  maxFiles?: number              // 最大文件数 (默认: 1000)
  includeExtensions?: string[]   // 包含的文件扩展名
  excludePatterns?: string[]     // 排除的路径模式
  onProgress?: (progress: ScanProgress) => void  // 进度回调
}
```

### PostprocessSettings

```typescript
interface PostprocessSettings {
  maxFilesN: number             // 最大文件数
  maxTokensPerFile?: number     // 每个文件的最大 token 数
}
```

## 注意事项

1. **性能**: 大型项目的首次扫描可能需要一些时间，建议设置合理的 `maxFiles` 限制
2. **内存使用**: AST 数据库会占用一定内存，可以通过 `getDetailedStatus()` 监控大小
3. **文件监控**: 当前版本不支持自动文件变更检测，需要手动重新扫描
4. **并发**: 数据库操作是线程安全的，但建议避免同时进行多个大型扫描操作

## 故障排除

### 常见问题

1. **扫描失败**: 检查文件权限和路径是否正确
2. **内存不足**: 减少 `maxFiles` 或增加系统内存
3. **解析错误**: 某些文件可能包含语法错误，会被自动跳过并记录在错误统计中

### 调试

启用调试日志：

```typescript
import { createLogger, LogLevel } from '@services/astdb'

const logger = createLogger('Debug', LogLevel.DEBUG)
// 现在会看到详细的调试信息
```

查看错误统计：

```typescript
const errorStats = astDb.getErrorStats()
console.log('Errors:', errorStats.errors)
```
