syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "common.proto";

// WatchService provides methods for watching files in the IDE
service WatchService {
  // Subscribe to file changes
  rpc subscribeToFile(SubscribeToFileRequest) returns (stream FileChangeEvent);
}

// Request to subscribe to file changes
message SubscribeToFileRequest {
  cline.Metadata metadata = 1;
  string path = 2;
}

// Event representing a file change
message FileChangeEvent {
  enum ChangeType {
    CREATED = 0;
    CHANGED = 1;
    DELETED = 2;
  }
  
  string path = 1;
  ChangeType type = 2;
  string content = 3; // Optional content of the file after change
}
