{"name": "eslint-plugin-eslint-rules", "version": "1.0.0", "description": "Custom ESLint rules for Cline", "main": "index.js", "scripts": {"test": "mocha --no-config --require ts-node/register __tests__/**/*.test.ts"}, "keywords": ["eslint", "eslintplugin"], "author": "Cline Bot Inc.", "license": "Apache-2.0", "dependencies": {"@typescript-eslint/utils": "^8.33.0"}, "devDependencies": {"@types/eslint": "^8.0.0", "@types/mocha": "^10.0.7", "@types/node": "^20.0.0", "@typescript-eslint/parser": "^7.14.1", "eslint": "^8.57.0", "mocha": "^10.0.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "peerDependencies": {"eslint": ">=8.0.0"}}