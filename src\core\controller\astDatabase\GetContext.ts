/**
 * Get Context Controller
 */

import { Controller } from ".."
import { GetContextResponse } from "@shared/proto/astdb"
import type { GetContextRequest } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function GetContext(controller: Controller, request: GetContextRequest): Promise<GetContextResponse> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.getContext(request)

		return response
	} catch (error) {
		console.error("Failed to get context:", error)

		// Return empty response if service is not available
		return GetContextResponse.create({
			contextFiles: [],
			extraContext: "",
		})
	}
}
