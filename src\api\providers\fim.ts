import { SingleCompletionHandler } from "../index"

/**
 * Options for FIM (Fill in the Middle) API handler
 */
export interface FimHandlerOptions {
	apiKey: string
	baseUrl: string
	requestTimeoutMs?: number
	customHeaders?: Record<string, string>
	maxTokens?: number
	temperature?: number
}

/**
 * FIM (Fill in the Middle) API handler for autocomplete
 * Supports the Fill in the Middle API format with prompt, suffix, and stream parameters
 */
export class FimHandler implements SingleCompletionHandler {
	private options: FimHandlerOptions

	constructor(options: FimHandlerOptions) {
		this.options = options
	}

	/**
	 * Complete a prompt using FIM API format
	 * @param prompt The code context before the cursor
	 * @param suffix The code context after the cursor (optional)
	 * @returns Promise<string> The completion text
	 */
	async completePrompt(prompt: string, suffix?: string): Promise<string> {
		// Limit context size to prevent overly large requests
		const maxContextLength = 8000 // characters
		const limitedPrompt = this.limitContextSize(prompt, maxContextLength * 0.7)
		const limitedSuffix = this.limitContextSize(suffix || "", maxContextLength * 0.3)

		console.log(`🚀📏 FIM context sizes - prompt: ${limitedPrompt.length} chars, suffix: ${limitedSuffix.length} chars`)
		const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`

		const headers: Record<string, string> = {
			"Content-Type": "application/json",
			Authorization: `Bearer ${this.options.apiKey}`,
			...this.options.customHeaders,
		}

		const body = {
			prompt: limitedPrompt,
			suffix: limitedSuffix,
			stream: false, // Non-streaming for simple completion
			max_tokens: this.options.maxTokens || 100, // Use configured max tokens
			temperature: this.options.temperature || 0.1, // Use configured temperature
		}

		try {
			const controller = new AbortController()
			const timeoutId = setTimeout(() => {
				controller.abort()
			}, this.options.requestTimeoutMs || 30000)

			const response = await fetch(url, {
				method: "POST",
				headers,
				body: JSON.stringify(body),
				signal: controller.signal,
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				const errorText = await response.text()
				const responseHeaders: Record<string, string> = {}
				response.headers.forEach((value, key) => {
					responseHeaders[key] = value
				})
				throw new Error(`FIM API request failed:
URL: ${url}
HTTP Status: ${response.status} ${response.statusText}
Response Headers: ${JSON.stringify(responseHeaders)}
Error Message: ${errorText}`)
			}

			const data = await response.json()
			console.log("FIM API non-streaming response:", data) // Debug log

			// Extract completion text from response
			// The exact format may vary depending on the FIM API implementation
			if (data.choices && data.choices.length > 0) {
				const text = data.choices[0].text || ""
				console.log("FIM API extracted text:", text) // Debug log
				return text
			}

			// Fallback for different response formats
			if (data.completion) {
				console.log("FIM API using completion field:", data.completion) // Debug log
				return data.completion
			}

			if (data.text) {
				console.log("FIM API using text field:", data.text) // Debug log
				return data.text
			}

			console.warn("FIM API: No text found in response:", data) // Debug log
			return ""
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					throw new Error(`FIM API request timed out:
URL: ${url}
Timeout: ${this.options.requestTimeoutMs || 30000}ms`)
				}
				throw new Error(`FIM API request failed:
URL: ${url}
Error: ${error.message}`)
			}
			throw new Error(`FIM API request failed with unknown error:
URL: ${url}`)
		}
	}

	/**
	 * Complete a prompt with streaming support and retry mechanism
	 * @param prompt The code context before the cursor
	 * @param suffix The code context after the cursor (optional)
	 * @returns AsyncGenerator<string> Stream of completion chunks
	 */
	async *completePromptStream(prompt: string, suffix?: string): AsyncGenerator<string> {
		const maxRetries = 2
		let lastError: Error | null = null

		for (let attempt = 0; attempt <= maxRetries; attempt++) {
			try {
				console.log(`FIM API streaming attempt ${attempt + 1}/${maxRetries + 1}`)
				yield* this.completePromptStreamInternal(prompt, suffix)
				return // Success, exit retry loop
			} catch (error) {
				lastError = error as Error
				console.warn(`FIM API streaming attempt ${attempt + 1} failed:`, error)

				// Don't retry on certain errors
				if (error instanceof Error && error.name === "AbortError") {
					throw error // Don't retry timeout errors
				}

				// Wait before retry (exponential backoff)
				if (attempt < maxRetries) {
					const delay = Math.pow(2, attempt) * 1000 // 1s, 2s, 4s...
					console.log(`FIM API retrying in ${delay}ms...`)
					await new Promise((resolve) => setTimeout(resolve, delay))
				}
			}
		}

		// All retries failed
		throw lastError || new Error("FIM API streaming failed after all retries")
	}

	/**
	 * Internal streaming method without retry logic
	 */
	private async *completePromptStreamInternal(prompt: string, suffix?: string): AsyncGenerator<string> {
		// Limit context size to prevent overly large requests
		const maxContextLength = 8000 // characters
		const limitedPrompt = this.limitContextSize(prompt, maxContextLength * 0.7)
		const limitedSuffix = this.limitContextSize(suffix || "", maxContextLength * 0.3)

		console.log(
			`🚀📏 FIM streaming context sizes - prompt: ${limitedPrompt.length} chars, suffix: ${limitedSuffix.length} chars`,
		)
		const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`

		const headers: Record<string, string> = {
			"Content-Type": "application/json",
			Authorization: `Bearer ${this.options.apiKey}`,
			Accept: "text/event-stream",
			"Cache-Control": "no-cache",
			Connection: "keep-alive",
			...this.options.customHeaders,
		}

		const body = {
			prompt: limitedPrompt,
			suffix: limitedSuffix,
			stream: true,
			max_tokens: this.options.maxTokens || 100,
			temperature: this.options.temperature || 0.1,
		}

		try {
			const controller = new AbortController()
			const timeoutId = setTimeout(() => {
				controller.abort()
			}, this.options.requestTimeoutMs || 30000)

			const response = await fetch(url, {
				method: "POST",
				headers,
				body: JSON.stringify(body),
				signal: controller.signal,
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				const errorText = await response.text()
				const responseHeaders: Record<string, string> = {}
				response.headers.forEach((value, key) => {
					responseHeaders[key] = value
				})
				throw new Error(`FIM API streaming request failed:
URL: ${url}
HTTP Status: ${response.status} ${response.statusText}
Response Headers: ${JSON.stringify(responseHeaders)}
Error Message: ${errorText}`)
			}

			if (!response.body) {
				throw new Error("No response body for streaming request")
			}

			const reader = response.body.getReader()
			const decoder = new TextDecoder()
			let buffer = "" // Buffer for incomplete lines

			try {
				while (true) {
					const { done, value } = await reader.read()
					if (done) {
						console.log("FIM API stream completed normally")
						break
					}

					const chunk = decoder.decode(value, { stream: true })
					console.log("FIM API received chunk:", chunk)

					// Add to buffer and split by lines
					buffer += chunk
					const lines = buffer.split("\n")

					// Keep the last incomplete line in buffer
					buffer = lines.pop() || ""

					for (const line of lines) {
						if (line.trim() === "") {
							continue
						}
						if (line.startsWith("data: ")) {
							const data = line.slice(6)
							if (data === "[DONE]") {
								continue
							}

							try {
								const parsed = JSON.parse(data)
								console.log("FIM API parsed response:", parsed) // Debug log

								if (parsed.choices && parsed.choices.length > 0) {
									const choice = parsed.choices[0]
									// Handle both streaming formats: text or delta.text
									const text = choice.text || choice.delta?.text || ""
									if (text) {
										console.log("FIM API yielding text:", text) // Debug log
										yield text
									}
								}
							} catch (e) {
								console.warn("FIM API JSON parse error:", e, "Data:", data) // Debug log
								// Skip invalid JSON lines but log the error
								continue
							}
						}
					}
				}
			} finally {
				reader.releaseLock()
			}
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					throw new Error(`FIM API streaming request timed out:
URL: ${url}
Timeout: ${this.options.requestTimeoutMs || 30000}ms`)
				}

				// Handle specific connection errors
				if (error.message.includes("terminated") || error.message.includes("aborted")) {
					throw new Error(`FIM API streaming connection terminated:
URL: ${url}
Error: ${error.message}
Possible causes: Server closed connection, network instability, or proxy interference
Suggestion: Check server logs, network connectivity, or try reducing request frequency`)
				}

				throw new Error(`FIM API streaming request failed:
URL: ${url}
Error: ${error.message}
Error Type: ${error.name}
Stack: ${error.stack}`)
			}
			throw new Error(`FIM API streaming request failed with unknown error:
URL: ${url}
Error Type: ${typeof error}
Error Value: ${String(error)}`)
		}
	}

	/**
	 * Limit the size of context to prevent overly large requests
	 * @param text The text to limit
	 * @param maxLength Maximum length in characters
	 * @returns Limited text
	 */
	private limitContextSize(text: string, maxLength: number): string {
		if (text.length <= maxLength) {
			return text
		}

		// For prompt (before cursor), keep the end (most recent context)
		// For suffix (after cursor), keep the beginning
		const lines = text.split("\n")
		let result = ""
		let currentLength = 0

		// If this looks like a prompt (no leading whitespace on first line),
		// keep the end (most recent context)
		if (!text.startsWith(" ") && !text.startsWith("\t")) {
			// Keep lines from the end
			for (let i = lines.length - 1; i >= 0; i--) {
				const line = lines[i]
				if (currentLength + line.length + 1 > maxLength) {
					break
				}
				result = line + (result ? "\n" + result : "")
				currentLength += line.length + 1
			}
		} else {
			// Keep lines from the beginning (for suffix)
			for (const line of lines) {
				if (currentLength + line.length + 1 > maxLength) {
					break
				}
				result += (result ? "\n" : "") + line
				currentLength += line.length + 1
			}
		}

		return result
	}

	/**
	 * Test the connection to the FIM API
	 * @returns Promise<boolean> True if connection is healthy
	 */
	async testConnection(): Promise<boolean> {
		try {
			const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`
			const headers: Record<string, string> = {
				"Content-Type": "application/json",
				Authorization: `Bearer ${this.options.apiKey}`,
				...this.options.customHeaders,
			}

			// Send a minimal test request
			const response = await fetch(url, {
				method: "POST",
				headers,
				body: JSON.stringify({
					prompt: "test",
					suffix: "",
					stream: false,
					max_tokens: 1,
					temperature: 0.1,
				}),
				signal: AbortSignal.timeout(5000), // 5 second timeout for health check
			})

			console.log(`FIM API health check: ${response.status} ${response.statusText}`)
			return response.ok || response.status === 400 // 400 might be expected for minimal request
		} catch (error) {
			console.warn("FIM API health check failed:", error)
			return false
		}
	}
}
