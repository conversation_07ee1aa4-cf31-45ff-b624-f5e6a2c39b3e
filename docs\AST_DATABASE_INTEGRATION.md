# AST Database Integration Guide

本文档介绍如何使用新集成的 AST 数据库功能来增强代码自动补全和上下文检索。

## 概述

AST 数据库集成为 VSCode 扩展提供了以下功能：

1. **Codebase 管理界面** - 在设置中管理 AST 数据库
2. **增强的自动补全** - 基于 AST 分析的智能上下文检索
3. **性能监控** - 实时监控 AST 数据库性能
4. **错误处理** - 完善的错误恢复和超时保护

## 功能特性

### 1. Codebase 管理界面

在 VSCode 扩展设置中新增了 "Codebase" 标签页，提供：

- **扫描工作区** - 一键扫描当前工作区建立 AST 索引
- **实时进度** - 显示扫描进度、速度和 ETA
- **状态监控** - 查看数据库状态、文件数、定义数等
- **数据库管理** - 重新扫描、清除数据库等操作

#### 使用方法

1. 打开 VSCode 设置 (Ctrl+,)
2. 切换到 "Codebase" 标签页
3. 点击 "Scan Workspace" 开始扫描
4. 监控扫描进度和状态

### 2. 增强的自动补全

AST 数据库为自动补全提供了智能上下文检索：

- **符号定义** - 基于 AST 分析的精确符号定义
- **相关符号** - 智能推荐相关的类、函数、变量
- **上下文感知** - 根据当前代码位置提供相关上下文
- **性能优化** - 缓存和超时保护确保响应速度

#### 配置选项

在自动补全设置中可以配置：

```typescript
{
  "useAstDatabase": true,           // 启用 AST 数据库
  "astDatabaseTimeout": 2000,       // 超时时间 (毫秒)
  "astDatabaseMaxDefinitions": 10   // 最大定义数量
}
```

### 3. 支持的编程语言

- JavaScript/TypeScript (.js, .jsx, .ts, .tsx)
- Python (.py)
- Rust (.rs)
- Go (.go)
- C/C++ (.c, .h, .cpp, .hpp)
- C# (.cs)
- Ruby (.rb)
- Java (.java)
- PHP (.php)
- Swift (.swift)
- Kotlin (.kt)

## API 参考

### AstDatabaseService

主要的后端服务类，提供 AST 数据库操作：

```typescript
class AstDatabaseService {
  // 初始化服务
  async initialize(workspacePath: string): Promise<void>
  
  // 获取数据库状态
  async getDatabaseStatus(): Promise<DatabaseStatusResponse>
  
  // 开始工作区扫描
  async startWorkspaceScan(request: StartScanRequest): Promise<void>
  
  // 搜索定义
  async searchDefinitions(request: SearchDefinitionsRequest): Promise<SearchDefinitionsResponse>
  
  // 获取上下文
  async getContext(request: GetContextRequest): Promise<GetContextResponse>
  
  // 获取相关符号
  async getRelatedSymbols(request: GetRelatedSymbolsRequest): Promise<GetRelatedSymbolsResponse>
  
  // 获取统计信息
  async getStatistics(): Promise<GetStatisticsResponse>
  
  // 清除数据库
  async clearDatabase(): Promise<void>
}
```

### ContextGatherer 集成

自动补全的上下文收集器已集成 AST 数据库：

```typescript
class ContextGatherer {
  // 启用/禁用 AST 数据库
  setUseAstDatabase(enabled: boolean): void
  
  // 获取性能指标
  getAstPerformanceMetrics(): PerformanceMetrics
  
  // 重置性能指标
  resetAstPerformanceMetrics(): void
  
  // 获取缓存统计
  getAstCacheStats(): CacheStats
}
```

## 性能监控

### 监控指标

AST 数据库提供详细的性能监控：

```typescript
interface PerformanceMetrics {
  totalRequests: number           // 总请求数
  successfulRequests: number      // 成功请求数
  failedRequests: number          // 失败请求数
  timeouts: number               // 超时次数
  averageResponseTime: number     // 平均响应时间
  maxResponseTime: number         // 最大响应时间
  minResponseTime: number         // 最小响应时间
  errorRate: number              // 错误率
}
```

### 健康检查

系统自动进行健康检查，监控：

- 错误率 (>20% 警告, >50% 不健康)
- 响应时间 (>5s 降级)
- 超时率 (>10% 不健康)
- 内存使用情况

### 获取状态报告

```typescript
const service = AstDatabaseService.getInstance()
const report = service.getStatusReport()
console.log(report)
```

输出示例：
```
AST Database Status Report
=========================
Health: HEALTHY ✓
Uptime: 2.5 hours
Total Requests: 1,234
Success Rate: 98.5%
Average Response Time: 145ms
Memory Usage: 45.2MB

No issues detected
```

## 错误处理

### 超时保护

所有 AST 数据库操作都有超时保护：

- 默认超时：2 秒
- 可配置超时时间
- 超时后自动降级到普通上下文

### 错误恢复

- **数据库不可用** - 自动降级到 LSP 和文件上下文
- **扫描失败** - 记录错误，允许重新扫描
- **查询错误** - 返回空结果，不影响自动补全

### 缓存机制

- **LRU 缓存** - 最近使用的上下文结果
- **5 分钟 TTL** - 自动过期机制
- **内存限制** - 最多缓存 100 个结果

## 最佳实践

### 1. 扫描策略

- **首次使用** - 扫描整个工作区建立完整索引
- **增量更新** - 文件变更时自动更新（计划中）
- **定期重扫** - 大量变更后重新扫描

### 2. 性能优化

- **合理设置超时** - 根据项目大小调整超时时间
- **限制定义数量** - 避免返回过多定义影响性能
- **监控指标** - 定期检查性能指标和健康状态

### 3. 故障排除

#### 扫描失败
1. 检查文件权限
2. 确认工作区路径正确
3. 查看错误日志
4. 尝试减少 maxFiles 设置

#### 自动补全慢
1. 检查 AST 数据库超时设置
2. 查看性能指标
3. 考虑禁用 AST 数据库
4. 清除并重建数据库

#### 内存使用过高
1. 检查数据库大小
2. 清除缓存
3. 减少 maxDefinitions 设置
4. 重启扩展

## 开发指南

### 添加新语言支持

1. 在 tree-sitter 中添加语言解析器
2. 更新 `includeExtensions` 配置
3. 测试 AST 解析和定义提取

### 扩展 API

1. 在 `astdb.proto` 中定义新的 gRPC 接口
2. 在 `AstDatabaseService` 中实现业务逻辑
3. 在 `AstDatabaseGrpcService` 中添加 gRPC 端点
4. 更新前端界面（如需要）

### 调试技巧

1. **启用调试日志**：
   ```typescript
   const logger = createLogger("Debug", LogLevel.DEBUG)
   ```

2. **监控性能**：
   ```typescript
   const metrics = service.getPerformanceMetrics()
   console.log("AST DB Performance:", metrics)
   ```

3. **检查健康状态**：
   ```typescript
   const health = service.getHealthStatus()
   if (!health.isHealthy) {
     console.warn("AST DB Issues:", health.issues)
   }
   ```

## 未来计划

- **增量更新** - 文件变更时自动更新 AST 索引
- **语义搜索** - 基于语义相似性的符号搜索
- **跨项目索引** - 支持多个项目的统一索引
- **云端同步** - 团队共享的 AST 索引
- **更多语言** - 支持更多编程语言

## 支持

如遇问题，请：

1. 查看 VSCode 开发者控制台的错误日志
2. 检查 AST 数据库状态报告
3. 尝试重新扫描工作区
4. 提交 Issue 并附上日志信息
