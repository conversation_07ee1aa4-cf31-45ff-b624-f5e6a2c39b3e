# Autocomplete UI 简化

## 修改内容

### 去掉条件显示，直接显示所有配置字段

#### 修改前
```tsx
{localSettings.provider !== "fim" && (
  <>
    <ApiKeyField value={localSettings.apiKey || ""} ... />
    <BaseUrlField value={localSettings.apiBaseUrl || ""} ... />
  </>
)}

{localSettings.provider === "fim" && (
  <>
    <ApiKeyField value={localSettings.fim?.apiKey || ""} ... />
    <BaseUrlField value={localSettings.fim?.baseUrl || ""} ... />
  </>
)}
```

#### 修改后
```tsx
<ApiKeyField
  value={localSettings.apiKey || ""}
  onChange={handleInputChange("apiKey")}
  providerName="OpenAI Compatible API"
  placeholder="Enter your API key..."
/>

<BaseUrlField
  value={localSettings.apiBaseUrl || ""}
  onChange={handleInputChange("apiBaseUrl")}
  placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
  label="API Base URL"
/>

<ApiKeyField
  value={localSettings.fim?.apiKey || ""}
  onChange={handleInputChange("fim.apiKey")}
  providerName="FIM API"
  placeholder="Enter your FIM API key..."
/>

<BaseUrlField
  value={localSettings.fim?.baseUrl || ""}
  onChange={handleInputChange("fim.baseUrl")}
  placeholder="https://your-fim-api.com/v1"
  label="FIM API Base URL"
/>
```

## 改进效果

### 1. **简化用户体验**
- 不需要切换 Provider 来查看不同的配置选项
- 所有配置字段始终可见，用户可以一次性配置所有选项
- 减少了界面的动态变化，提供更稳定的用户体验

### 2. **提高配置效率**
- 用户可以同时配置 OpenAI 和 FIM 的设置
- 不需要在不同 Provider 之间切换来填写配置
- 便于比较和管理不同 API 的配置

### 3. **更清晰的配置结构**
```
Provider 选择: [OpenAI Compatible ▼] [FIM (Fill in the Middle)]

OpenAI Compatible API:
├── API Key: [输入框]
└── API Base URL: [输入框]

FIM API:
├── API Key: [输入框]
└── API Base URL: [输入框]

通用设置:
├── Model ID: [输入框]
├── Max Tokens: [输入框]
├── Temperature: [输入框]
├── Request Timeout: [输入框]
├── Debounce Time: [输入框]
└── Use Prompt Cache: [复选框]
```

### 4. **保持功能完整性**
- Provider 选择仍然有效，决定实际使用哪个 API
- 所有配置都会被保存，无论当前选择哪个 Provider
- 配置验证逻辑保持不变

## 使用场景

### 1. **多 API 环境**
用户可能需要在不同情况下使用不同的 API：
- 开发环境使用本地 FIM API
- 生产环境使用 OpenAI Compatible API
- 备用 API 配置以防主要 API 不可用

### 2. **快速切换**
- 预先配置好两种 API 的设置
- 通过 Provider 下拉框快速切换
- 无需重新输入配置信息

### 3. **配置管理**
- 一次性查看所有配置选项
- 便于检查配置的完整性
- 减少配置错误的可能性

## 技术实现

### 配置字段映射
```typescript
// OpenAI Compatible
apiKey: string
apiBaseUrl: string

// FIM
fim.apiKey: string
fim.baseUrl: string

// 通用设置
provider: "openai" | "fim"
modelId: string
maxTokens: number
temperature: number
// ...
```

### 验证逻辑
```typescript
// 根据选择的 provider 验证相应的配置
if (settings.provider === "fim") {
  // 验证 FIM 配置
  if (!settings.fim?.apiKey) errors.push("FIM API key required")
  if (!settings.fim?.baseUrl) errors.push("FIM base URL required")
} else {
  // 验证 OpenAI 配置
  if (!settings.apiKey) errors.push("API key required")
}
```

## 用户体验改进

### 之前的问题
- 需要切换 Provider 才能看到相应的配置选项
- 容易忘记配置某个 Provider 的设置
- 界面动态变化可能造成困惑

### 现在的优势
- 所有配置选项一目了然
- 可以预先配置多个 API 选项
- 界面稳定，用户体验更好
- 配置更加直观和高效

这个修改让 autocomplete 配置页面更加用户友好，提供了更好的配置管理体验！
