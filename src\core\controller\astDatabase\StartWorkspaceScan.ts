/**
 * Start Workspace Scan Controller
 */

import { Controller } from ".."
import { Empty } from "@shared/proto/common"
import type { StartScanRequest } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function StartWorkspaceScan(controller: Controller, request: StartScanRequest): Promise<Empty> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.startWorkspaceScan(request)

		return response
	} catch (error) {
		console.error("Failed to start workspace scan:", error)
		throw error
	}
}
