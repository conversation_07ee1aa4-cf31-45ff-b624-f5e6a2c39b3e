---
title: QAX
description: How to configure <PERSON>line to use QAX API
---

# QAX

QAX is an AI provider that offers OpenAI-compatible API endpoints. This guide will help you configure Cline to use QAX's services.

## Configuration

To use QAX with <PERSON>line, you'll need:

1. **QAX API Key**: Your authentication key for accessing QAX services
2. **Base URL**: The API endpoint URL for QAX services
3. **Model ID**: The specific model you want to use

### Setup Steps

1. Open Cline's settings
2. Select "QAX" from the API Provider dropdown
3. Enter your QAX API Key (this will be stored securely locally)
4. Enter the Base URL for the QAX API endpoint
5. Enter the Model ID you want to use

### Example Configuration

```
API Provider: QAX
QAX API Key: your-qax-api-key-here
Base URL: https://api.qax.example.com/v1
Model ID: qax-model-name
```

## Features

QAX provider supports:

-   Text generation and conversation
-   All standard Cline features through OpenAI-compatible API
-   Custom model configurations

## Notes

-   The QAX provider uses the same underlying implementation as OpenAI Compatible providers
-   Your API key is stored locally and only used for making API requests
-   Make sure your QAX account has sufficient credits for API usage
-   Contact QAX support for specific model availability and pricing information

## Troubleshooting

If you encounter issues:

1. **Authentication errors**: Verify your API key is correct
2. **Connection errors**: Check the Base URL format and network connectivity
3. **Model errors**: Ensure the Model ID is valid and available in your QAX account

For additional support, refer to QAX's official documentation or contact their support team.
