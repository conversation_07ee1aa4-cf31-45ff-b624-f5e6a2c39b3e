syntax = "proto3";

package cline;
option java_package = "bot.cline.proto";
option java_multiple_files = true;

import "common.proto";

// AST Database Service
service AstDatabaseService {
  rpc GetDatabaseStatus(EmptyRequest) returns (DatabaseStatusResponse);
  rpc StartWorkspaceScan(StartScanRequest) returns (Empty);
  rpc SearchDefinitions(SearchDefinitionsRequest) returns (SearchDefinitionsResponse);
  rpc GetContext(GetContextRequest) returns (GetContextResponse);
  rpc GetRelatedSymbols(GetRelatedSymbolsRequest) returns (GetRelatedSymbolsResponse);
  rpc GetStatistics(GetStatisticsRequest) returns (GetStatisticsResponse);
  rpc ClearDatabase(ClearDatabaseRequest) returns (Empty);
}

// Request/Response Messages

message ScanProgress {
  int32 total_files = 1;
  int32 processed_files = 2;
  string current_file = 3;
  repeated string errors = 4;
  string status = 5;
  string start_time = 6;
  int64 elapsed_ms = 7;
  int64 estimated_remaining_ms = 8;
  double files_per_second = 9;
  int32 definitions_found = 10;
  int32 usages_found = 11;
  int64 bytes_processed = 12;
}

message DatabaseStatus {
  string astate = 1;
  int32 files_total = 2;
  int32 files_unparsed = 3;
  int32 ast_index_files_total = 4;
  int32 ast_index_symbols_total = 5;
  int32 ast_index_usages_total = 6;
  bool ast_max_files_hit = 7;
  optional string last_updated = 8;
  optional int64 db_size_bytes = 9;
  optional int32 unique_files = 10;
  optional double average_definitions_per_file = 11;
  optional double average_usages_per_definition = 12;
}

message ScanOptions {
  optional int32 max_files = 1;
  repeated string include_extensions = 2;
  repeated string exclude_patterns = 3;
}

message StartScanRequest {
  Metadata metadata = 1;
  string workspace_path = 2;
  optional ScanOptions options = 3;
}

message ScanProgressResponse {
  optional ScanProgress progress = 1;
}

message DatabaseStatusResponse {
  optional DatabaseStatus status = 1;
}

message SearchDefinitionsRequest {
  Metadata metadata = 1;
  string symbol_name = 2;
  optional int32 limit = 3;
}

message AstUsage {
  repeated string targets_for_guesswork = 1;
  string resolved_as = 2;
  string debug_hint = 3;
  int32 uline = 4;
}

message AstDefinition {
  repeated string official_path = 1;
  string symbol_type = 2;
  string resolved_type = 3;
  string this_is_a_class = 4;
  repeated string this_class_derived_from = 5;
  string cpath = 6;
  int32 decl_line1 = 7;
  int32 decl_line2 = 8;
  int32 body_line1 = 9;
  int32 body_line2 = 10;
  repeated AstUsage usages = 11;
}

message SearchDefinitionsResponse {
  repeated AstDefinition definitions = 1;
}

message GetContextRequest {
  Metadata metadata = 1;
  string file_path = 2;
  int32 line = 3;
  int32 character = 4;
  optional int32 max_tokens = 5;
}

message ContextFile {
  string file_name = 1;
  string file_content = 2;
  int32 line1 = 3;
  int32 line2 = 4;
  repeated string symbols = 5;
  int32 gradient_type = 6;
  double usefulness = 7;
}

message GetContextResponse {
  repeated ContextFile context_files = 1;
  string extra_context = 2;
}

message GetRelatedSymbolsRequest {
  Metadata metadata = 1;
  string symbol_path = 2;
  optional int32 max_results = 3;
}

message GetRelatedSymbolsResponse {
  repeated string symbols = 1;
}

message ClearDatabaseRequest {
  Metadata metadata = 1;
}

message GetStatisticsRequest {
  Metadata metadata = 1;
}

message FileDefinitionCount {
  string file = 1;
  int32 count = 2;
}

message DatabaseStatistics {
  int32 total_definitions = 1;
  int32 total_usages = 2;
  int32 total_files = 3;
  map<string, int32> definitions_by_type = 4;
  repeated FileDefinitionCount files_with_most_definitions = 5;
}

message GetStatisticsResponse {
  optional DatabaseStatistics statistics = 1;
}
