import { VSCodeTextField } from "@vscode/webview-ui-toolkit/react"

/**
 * Props for the BaseUrlField component
 */
interface BaseUrlFieldProps {
	value: string | undefined
	onChange: (value: string) => void
	defaultValue?: string
	label?: string
	placeholder?: string
}

/**
 * A reusable component for entering base URLs
 */
export const BaseUrlField = ({
	value,
	onChange,
	defaultValue = "",
	label = "Base URL",
	placeholder = "https://api.example.com",
}: BaseUrlFieldProps) => {
	return (
		<VSCodeTextField
			value={value || ""}
			style={{ width: "100%" }}
			type="url"
			onInput={(e: any) => onChange(e.target.value)}
			placeholder={placeholder}>
			<span style={{ fontWeight: 500 }}>{label}</span>
		</VSCodeTextField>
	)
}
