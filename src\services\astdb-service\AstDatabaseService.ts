/**
 * AST Database Service
 * Provides backend functionality for AST database operations
 */

import * as vscode from "vscode"
import * as path from "path"
import {
	AstDB,
	WorkspaceScanner,
	scanWorkspaceForAST,
	formatScanProgress,
	createProgressReport,
	createLogger,
	LogLevel,
	type ScanProgress,
	type ScanOptions
} from "@services/astdb"
import { AstDatabaseMonitor } from "./AstDatabaseMonitor"
import { EmptyRequest, Empty } from "@shared/proto/common"
import {
	DatabaseStatus,
	DatabaseStatusResponse,
	ScanProgressResponse,
	SearchDefinitionsResponse,
	GetRelatedSymbolsResponse,
	GetStatisticsResponse
} from "@shared/proto/astdb"
import type {
	StartScanRequest,
	SearchDefinitionsRequest,
	GetContextRequest,
	GetContextResponse,
	GetRelatedSymbolsRequest,
	GetStatisticsRequest,
	ClearDatabaseRequest
} from "@shared/proto/astdb"

export class AstDatabaseService {
	private static instance: AstDatabaseService | null = null
	private astDb: AstDB | null = null
	private scanner: WorkspaceScanner | null = null
	private logger = createLogger("AstDatabaseService", LogLevel.INFO)
	private isScanning = false
	private scanProgressCallback: ((progress: ScanProgress) => void) | null = null
	private workspacePath: string | null = null
	private monitor = AstDatabaseMonitor.getInstance()

	private constructor() {
		this.logger.info("AstDatabaseService initialized")
	}

	public static getInstance(): AstDatabaseService {
		if (!AstDatabaseService.instance) {
			AstDatabaseService.instance = new AstDatabaseService()
		}
		return AstDatabaseService.instance
	}

	/**
	 * Get the current workspace path, with fallback logic
	 */
	private getCurrentWorkspacePath(): string | null {
		// First try to use the stored workspace path
		if (this.workspacePath) {
			return this.workspacePath
		}

		// Try to get from VSCode workspace
		const workspaceFolders = vscode.workspace.workspaceFolders
		if (workspaceFolders && workspaceFolders.length > 0) {
			return workspaceFolders[0].uri.fsPath
		}

		return null
	}

	/**
	 * Initialize the service with workspace path
	 */
	public async initialize(workspacePath?: string): Promise<void> {
		// Use provided path or try to detect current workspace
		const resolvedWorkspacePath = workspacePath || this.getCurrentWorkspacePath()

		if (!resolvedWorkspacePath) {
			throw new Error("No workspace path provided and no VSCode workspace folders available")
		}

		this.workspacePath = resolvedWorkspacePath
		this.logger.info("Initializing AST database service", { workspacePath: this.workspacePath })

		try {
			// Initialize AST database
			const dbPath = path.join(this.workspacePath, ".vscode", "ast-cache", "ast.json")
			this.astDb = new AstDB(dbPath)
			this.scanner = new WorkspaceScanner(this.astDb)

			this.logger.info("AST database service initialized successfully")
		} catch (error) {
			this.logger.error("Failed to initialize AST database service", error as Error)
			throw error
		}
	}

	/**
	 * Ensure the service is initialized with current workspace
	 */
	private async ensureInitialized(): Promise<void> {
		if (!this.astDb || !this.workspacePath) {
			// Try to auto-initialize if we have a workspace
			const currentWorkspacePath = this.getCurrentWorkspacePath()
			if (currentWorkspacePath) {
				await this.initialize(currentWorkspacePath)
			} else {
				throw new Error("AST database not initialized and no workspace available")
			}
		}
	}

	/**
	 * Get current database status
	 */
	public async getDatabaseStatus(request: EmptyRequest): Promise<DatabaseStatusResponse> {
		this.logger.debug("Getting database status")

		try {
			await this.ensureInitialized()
		} catch (error) {
			// If initialization fails, return an empty response indicating no database
			this.logger.warn("Database not available", error as Error)
			return DatabaseStatusResponse.create({})
		}

		try {
			if (!this.astDb) {
				throw new Error("AST database is not available after initialization")
			}
			const detailedStatus = this.astDb.getDetailedStatus()
			
			const status = DatabaseStatus.create({
				astate: detailedStatus.astate,
				filesTotal: detailedStatus.filesTotal,
				filesUnparsed: detailedStatus.filesUnparsed,
				astIndexFilesTotal: detailedStatus.astIndexFilesTotal,
				astIndexSymbolsTotal: detailedStatus.astIndexSymbolsTotal,
				astIndexUsagesTotal: detailedStatus.astIndexUsagesTotal,
				astMaxFilesHit: detailedStatus.astMaxFilesHit,
				lastUpdated: detailedStatus.lastUpdated.toISOString(),
				dbSizeBytes: detailedStatus.dbSizeBytes,
				uniqueFiles: detailedStatus.uniqueFiles,
				averageDefinitionsPerFile: detailedStatus.averageDefinitionsPerFile,
				averageUsagesPerDefinition: detailedStatus.averageUsagesPerDefinition
			})

			return DatabaseStatusResponse.create({ status })
		} catch (error) {
			this.logger.error("Failed to get database status", error as Error)
			throw error
		}
	}

	/**
	 * Start workspace scan
	 */
	public async startWorkspaceScan(request: StartScanRequest): Promise<Empty> {
		// Determine the workspace path to use
		let workspacePath = request.workspacePath

		// If no workspace path provided or empty, try to get from VSCode workspace
		if (!workspacePath || workspacePath.trim() === "") {
			const workspaceFolders = vscode.workspace.workspaceFolders
			if (workspaceFolders && workspaceFolders.length > 0) {
				workspacePath = workspaceFolders[0].uri.fsPath
				this.logger.info("Using VSCode workspace folder as scan path", { workspacePath })
			} else {
				throw new Error("No workspace path provided and no VSCode workspace folders available")
			}
		}

		this.logger.info("Starting workspace scan", {
			workspacePath,
			options: request.options
		})

		// Ensure service is initialized with the correct workspace
		if (!this.astDb || !this.scanner || this.workspacePath !== workspacePath) {
			await this.initialize(workspacePath)
		}

		if (this.isScanning) {
			throw new Error("Scan already in progress")
		}

		try {
			this.isScanning = true
			
			const scanOptions: ScanOptions = {
				maxFiles: request.options?.maxFiles || 1000,
				includeExtensions: request.options?.includeExtensions || [
					"js", "jsx", "ts", "tsx", "py", "rs", "go", "c", "h", 
					"cpp", "hpp", "cs", "rb", "java", "php", "swift", "kt"
				],
				excludePatterns: request.options?.excludePatterns || [
					"node_modules", ".git", "dist", "build", ".vscode"
				],
				onProgress: (progress) => {
					this.scanProgressCallback?.(progress)
				}
			}

			// Start scan in background
			this.performScan(workspacePath, scanOptions)
				.catch(error => {
					this.logger.error("Scan failed", error as Error)
					this.isScanning = false
				})

			return Empty.create({})
		} catch (error) {
			this.isScanning = false
			this.logger.error("Failed to start workspace scan", error as Error)
			throw error
		}
	}

	/**
	 * Set scan progress callback
	 */
	public setScanProgressCallback(callback: (progress: ScanProgress) => void): void {
		this.scanProgressCallback = callback
	}

	/**
	 * Perform the actual scan
	 */
	private async performScan(workspacePath: string, options: ScanOptions): Promise<void> {
		try {
			if (!this.scanner) {
				throw new Error("Scanner not initialized")
			}

			await this.scanner.scanWorkspace(workspacePath, options)
			this.isScanning = false
			this.logger.info("Workspace scan completed successfully")
		} catch (error) {
			this.isScanning = false
			this.logger.error("Workspace scan failed", error as Error)
			throw error
		}
	}

	/**
	 * Search definitions
	 */
	public async searchDefinitions(request: SearchDefinitionsRequest): Promise<SearchDefinitionsResponse> {
		this.logger.debug("Searching definitions", { 
			symbolName: request.symbolName, 
			limit: request.limit 
		})

		if (!this.astDb) {
			throw new Error("AST database not initialized")
		}

		try {
			const definitions = await this.astDb.searchDefinitions(
				request.symbolName, 
				request.limit || 50
			)

			const protoDefinitions = definitions.map(def => ({
				officialPath: def.officialPath,
				symbolType: def.symbolType,
				resolvedType: def.resolvedType,
				thisIsAClass: def.thisIsAClass,
				thisClassDerivedFrom: def.thisClassDerivedFrom,
				cpath: def.cpath,
				declLine1: def.declLine1,
				declLine2: def.declLine2,
				bodyLine1: def.bodyLine1,
				bodyLine2: def.bodyLine2,
				usages: def.usages.map(usage => ({
					targetsForGuesswork: usage.targetsForGuesswork,
					resolvedAs: usage.resolvedAs,
					debugHint: usage.debugHint,
					uline: usage.uline
				}))
			}))

			return SearchDefinitionsResponse.create({ definitions: protoDefinitions })
		} catch (error) {
			this.logger.error("Failed to search definitions", error as Error)
			throw error
		}
	}

	/**
	 * Get context for autocomplete
	 */
	public async getContext(request: GetContextRequest): Promise<GetContextResponse> {
		const requestId = this.monitor.recordRequestStart()
		const startTime = Date.now()

		this.logger.debug("Getting context", {
			filePath: request.filePath,
			line: request.line,
			character: request.character,
			requestId
		})

		if (!this.astDb) {
			const error = new Error("AST database not initialized")
			this.monitor.recordRequestFailure(requestId, error, Date.now() - startTime)
			throw error
		}

		try {
			// Import the context retrieval functions
			const { retrieveAstBasedExtraContext, SimpleTokenizer, getSymbolContext } = await import("@services/astdb")
			
			const tokenizer = new SimpleTokenizer("starcoder", 0.5)
			const cursorPos = {
				file: request.filePath,
				line: request.line,
				character: request.character
			}

			const ppSettings = {
				maxFilesN: 5,
				maxTokensPerFile: request.maxTokens || 500
			}

			const contextUsed: Record<string, any> = {}
			
			// Get workspace paths for context retrieval
			let workspacePaths: string[] = []
			if (this.workspacePath) {
				workspacePaths = [this.workspacePath]
			} else {
				// Try to get from VSCode workspace
				const workspaceFolders = vscode.workspace.workspaceFolders
				if (workspaceFolders && workspaceFolders.length > 0) {
					workspacePaths = workspaceFolders.map(folder => folder.uri.fsPath)
				} else {
					// Fallback to current working directory if available
					try {
						workspacePaths = [process.cwd()]
					} catch (error) {
						// If process.cwd() is not available, use empty array
						workspacePaths = []
					}
				}
			}

			const extraContext = await retrieveAstBasedExtraContext(
				this.astDb,
				tokenizer,
				cursorPos.file,
				cursorPos,
				[Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER],
				ppSettings,
				request.maxTokens || 1000,
				contextUsed,
				workspacePaths
			)

			// Get symbol context as well
			const symbolContext = await getSymbolContext(this.astDb, "", 5)

			const contextFiles = symbolContext.map(ctx => ({
				fileName: ctx.fileName,
				fileContent: ctx.fileContent,
				line1: ctx.line1,
				line2: ctx.line2,
				symbols: ctx.symbols,
				gradientType: ctx.gradientType,
				usefulness: ctx.usefulness
			}))

			const result = {
				contextFiles,
				extraContext: extraContext || ""
			}

			this.monitor.recordRequestSuccess(requestId, Date.now() - startTime)
			return result
		} catch (error) {
			this.monitor.recordRequestFailure(requestId, error as Error, Date.now() - startTime)
			this.logger.error("Failed to get context", error as Error)
			throw error
		}
	}

	/**
	 * Get related symbols
	 */
	public async getRelatedSymbols(request: GetRelatedSymbolsRequest): Promise<GetRelatedSymbolsResponse> {
		this.logger.debug("Getting related symbols", { 
			symbolPath: request.symbolPath, 
			maxResults: request.maxResults 
		})

		if (!this.astDb) {
			throw new Error("AST database not initialized")
		}

		try {
			const { getRelatedSymbols } = await import("@services/astdb")
			
			const symbols = await getRelatedSymbols(
				this.astDb,
				request.symbolPath,
				request.maxResults || 10
			)

			return GetRelatedSymbolsResponse.create({ symbols })
		} catch (error) {
			this.logger.error("Failed to get related symbols", error as Error)
			throw error
		}
	}

	/**
	 * Get database statistics
	 */
	public async getStatistics(request: GetStatisticsRequest): Promise<GetStatisticsResponse> {
		this.logger.debug("Getting database statistics")

		if (!this.astDb) {
			throw new Error("AST database not initialized")
		}

		try {
			const stats = this.astDb.getStatistics()

			const statistics = {
				totalDefinitions: stats.totalDefinitions,
				totalUsages: stats.totalUsages,
				totalFiles: stats.totalFiles,
				definitionsByType: stats.definitionsByType,
				filesWithMostDefinitions: stats.filesWithMostDefinitions.map(item => ({
					file: item.file,
					count: item.count
				}))
			}

			return GetStatisticsResponse.create({ statistics })
		} catch (error) {
			this.logger.error("Failed to get statistics", error as Error)
			throw error
		}
	}

	/**
	 * Clear database
	 */
	public async clearDatabase(request: ClearDatabaseRequest): Promise<Empty> {
		this.logger.info("Clearing database")

		if (!this.astDb) {
			throw new Error("AST database not initialized")
		}

		try {
			await this.astDb.clearAll()
			this.logger.info("Database cleared successfully")
			return Empty.create({})
		} catch (error) {
			this.logger.error("Failed to clear database", error as Error)
			throw error
		}
	}

	/**
	 * Check if scanning is in progress
	 */
	public isCurrentlyScanning(): boolean {
		return this.isScanning
	}

	/**
	 * Get performance metrics
	 */
	public getPerformanceMetrics() {
		return this.monitor.getMetrics()
	}

	/**
	 * Get health status
	 */
	public getHealthStatus() {
		return this.monitor.getHealthStatus()
	}

	/**
	 * Get status report
	 */
	public getStatusReport(): string {
		return this.monitor.getStatusReport()
	}

	/**
	 * Reset performance metrics
	 */
	public resetMetrics(): void {
		this.monitor.resetMetrics()
	}

	/**
	 * Dispose of resources
	 */
	public async dispose(): Promise<void> {
		this.logger.info("Disposing AST database service")

		if (this.astDb) {
			await this.astDb.close()
			this.astDb = null
		}

		this.scanner = null
		this.scanProgressCallback = null
		this.isScanning = false
		this.monitor.dispose()
	}
}
