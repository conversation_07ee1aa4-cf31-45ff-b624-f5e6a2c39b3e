{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$tsc", "label": "npm: compile", "presentation": {"reveal": "silent"}}, {"type": "npm", "script": "protos", "problemMatcher": [], "isBackground": false, "presentation": {"reveal": "always"}, "options": {"env": {"IS_DEV": "true"}}}, {"type": "npm", "script": "build:webview", "group": "build", "problemMatcher": [], "label": "npm: build:webview", "presentation": {"reveal": "silent"}, "options": {"env": {"IS_DEV": "true"}}}, {"label": "watch", "dependsOn": ["npm: protos", "npm: build:webview", "npm: dev:webview", "npm: watch:tsc", "npm: watch:esbuild"], "presentation": {"reveal": "always"}, "group": "build"}, {"type": "npm", "script": "dev:webview", "group": "build", "problemMatcher": [{"pattern": [{"regexp": ".", "file": 1, "location": 2, "message": 3}], "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "."}}], "isBackground": true, "label": "npm: dev:webview", "dependsOn": ["npm: protos"], "presentation": {"group": "watch", "reveal": "always"}, "options": {"env": {"IS_DEV": "true"}}}, {"type": "npm", "script": "watch:esbuild", "group": "build", "problemMatcher": {"pattern": [{"regexp": "^✘ \\[ERROR\\] (.*)$", "message": 1}, {"regexp": "^\\s+(.*):(\\d+):(\\d+):$", "file": 1, "line": 2, "column": 3}], "background": {"activeOnStart": true, "beginsPattern": "^\\[watch\\] build started$", "endsPattern": "^\\[watch\\] build finished$"}}, "isBackground": true, "label": "npm: watch:esbuild", "dependsOn": ["npm: protos"], "presentation": {"group": "watch", "reveal": "always"}, "options": {"env": {"IS_DEV": "true"}}}, {"type": "npm", "script": "watch:tsc", "group": "build", "problemMatcher": "$tsc-watch", "isBackground": true, "label": "npm: watch:tsc", "dependsOn": ["npm: protos"], "presentation": {"group": "watch", "reveal": "always"}}, {"label": "compile-standalone", "type": "npm", "script": "compile-standalone", "group": "build", "problemMatcher": [], "presentation": {"reveal": "always"}}]}