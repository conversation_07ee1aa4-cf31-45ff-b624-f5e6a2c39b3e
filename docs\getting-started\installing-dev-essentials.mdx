---
title: "Installing Dev Essentials"
description: >-
    When you start coding, you'll need some essential development tools installed
    on your computer. <PERSON><PERSON> can help you install everything you need in a safe,
    guided way.
---

### 🧰 The Essential Tools

Here are the core tools you'll need for development:

-   **Node.js & npm:** Required for JavaScript and web development
-   **Git:** For tracking changes in your code and collaborating with others
-   **Package Managers:** Tools that make it easy to install other development tools
    -   Homebrew for macOS
    -   Chocolatey for Windows
    -   apt/yum for Linux

> 💡 **Tip:** These tools are the foundation of your developer toolkit. Installing them properly will set you up for success!

### 🚀 Let Cline Install Everything

Copy one of these prompts based on your operating system and paste it into **Cline**:

#### For macOS

```
Hello Cline! I need help setting up my Mac for software development. Could you please help me install the essential development tools like Homebrew, Node.js, Git, and any other core utilities that are commonly needed for coding? I'd like you to guide me through the process step-by-step.
```

#### For Windows

```
Hello Cline! I need help setting up my Windows PC for software development. Could you please help me install the essential development tools like Node.js, Git, and any other core utilities that are commonly needed for coding? I'd like you to guide me through the process step-by-step.
```

#### For Linux

```
Hello Cline! I need help setting up my Linux system for software development. Could you please help me install the essential development tools like Node.js, Git, and any other core utilities that are commonly needed for coding? I'd like you to guide me through the process step-by-step.
```

> ✅ **Pro Tip:** Cline will show you each command before running it. You stay in control the entire time!

### 🔍 What Will Happen

Cline will guide you through the following steps:

1. Installing the appropriate package manager for your system
2. Using the package manager to install Node.js and Git
3. Showing you the exact command before it runs (you approve each step!)
4. Verifying each installation is successful

> ⚠️ **Note:** You might need to enter your computer's password for some installations. This is normal!

### 💡 Why These Tools Are Important

-   **Node.js & npm:**
    -   Build websites with frameworks like React or Next.js
    -   Run JavaScript code
    -   Install JavaScript packages
-   **Git:**
    -   Save different versions of your code
    -   Collaborate with other developers
    -   Back up your work
-   **Package Managers:**
    -   Quickly install and update development tools
    -   Keep your environment organized and up to date

### 🧩 Notes

> 💡 **Tip:** The installation process is interactive — Cline will guide you step by step!

-   All commands are shown to you for approval before they run.
-   If you run into any issues, Cline will help troubleshoot them.
-   You may need to enter your computer's password for certain steps.

### 🧑‍💻 Additional Tips for New Coders

#### Understanding the Terminal

The Terminal is an application where you can type commands to interact with your computer.

-   **macOS:** Open it by searching for "Terminal" in Spotlight.
-   **Example:**

```
$ open -a Terminal
```

#### Understanding VS Code Features

-   **Terminal in VS Code:** Run commands directly from within VS Code!
    -   Go to **View > Terminal** or press \`Ctrl + \`\`.
    -   Example:

```
$ node -v
v16.14.0
```

-   **Document View:** Where you edit your code files.
    -   Open files from the Explorer panel on the left.
-   **Problems Section:** View errors or warnings in your code.
    -   Access it by clicking the lightbulb icon or **View > Problems**.

#### Common Features

-   **Command Line Interface (CLI):** A powerful tool for running commands.
-   **Permissions:** You might need to grant permissions to certain commands — this keeps your system secure.
