/**
 * gRPC Service implementation for AST Database
 */

import { AstDatabaseService } from "./AstDatabaseService"
import type { EmptyRequest, Empty } from "@shared/proto/common"
import {
	ScanProgressResponse,
	ScanProgress
} from "@shared/proto/astdb"
import type {
	DatabaseStatusResponse,
	StartScanRequest,
	SearchDefinitionsRequest,
	SearchDefinitionsResponse,
	GetContextRequest,
	GetContextResponse,
	GetRelatedSymbolsRequest,
	GetRelatedSymbolsResponse,
	GetStatisticsRequest,
	GetStatisticsResponse,
	ClearDatabaseRequest
} from "@shared/proto/astdb"

export class AstDatabaseGrpcService {
	private astService: AstDatabaseService

	constructor() {
		this.astService = AstDatabaseService.getInstance()
	}

	/**
	 * Initialize the service
	 */
	public async initialize(workspacePath?: string): Promise<void> {
		await this.astService.initialize(workspacePath)
	}

	/**
	 * Get database status
	 */
	public async getDatabaseStatus(request: EmptyRequest): Promise<DatabaseStatusResponse> {
		return await this.astService.getDatabaseStatus(request)
	}

	/**
	 * Start workspace scan
	 */
	public async startWorkspaceScan(request: StartScanRequest): Promise<Empty> {
		return await this.astService.startWorkspaceScan(request)
	}

	/**
	 * Search definitions
	 */
	public async searchDefinitions(request: SearchDefinitionsRequest): Promise<SearchDefinitionsResponse> {
		return await this.astService.searchDefinitions(request)
	}

	/**
	 * Get context for autocomplete
	 */
	public async getContext(request: GetContextRequest): Promise<GetContextResponse> {
		return await this.astService.getContext(request)
	}

	/**
	 * Get related symbols
	 */
	public async getRelatedSymbols(request: GetRelatedSymbolsRequest): Promise<GetRelatedSymbolsResponse> {
		return await this.astService.getRelatedSymbols(request)
	}

	/**
	 * Get database statistics
	 */
	public async getStatistics(request: GetStatisticsRequest): Promise<GetStatisticsResponse> {
		return await this.astService.getStatistics(request)
	}

	/**
	 * Clear database
	 */
	public async clearDatabase(request: ClearDatabaseRequest): Promise<Empty> {
		return await this.astService.clearDatabase(request)
	}

	/**
	 * Set up scan progress streaming
	 */
	public setupScanProgressStream(callback: (progress: ScanProgressResponse) => void): void {
		this.astService.setScanProgressCallback((progress) => {
			const response = ScanProgressResponse.create({
				progress: ScanProgress.create({
					totalFiles: progress.totalFiles,
					processedFiles: progress.processedFiles,
					currentFile: progress.currentFile,
					errors: progress.errors,
					status: progress.status,
					startTime: progress.startTime.toISOString(),
					elapsedMs: progress.elapsedMs,
					estimatedRemainingMs: progress.estimatedRemainingMs,
					filesPerSecond: progress.filesPerSecond,
					definitionsFound: progress.definitionsFound,
					usagesFound: progress.usagesFound,
					bytesProcessed: progress.bytesProcessed
				})
			})
			callback(response)
		})
	}

	/**
	 * Check if scanning is in progress
	 */
	public isScanning(): boolean {
		return this.astService.isCurrentlyScanning()
	}

	/**
	 * Dispose of resources
	 */
	public async dispose(): Promise<void> {
		await this.astService.dispose()
	}
}

// Export singleton instance
export const astDatabaseGrpcService = new AstDatabaseGrpcService()
