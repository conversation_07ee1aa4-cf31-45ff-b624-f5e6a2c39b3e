/**
 * Search Definitions Controller
 */

import { Controller } from ".."
import { SearchDefinitionsResponse } from "@shared/proto/astdb"
import type { SearchDefinitionsRequest } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function SearchDefinitions(
	controller: Controller,
	request: SearchDefinitionsRequest,
): Promise<SearchDefinitionsResponse> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.searchDefinitions(request)

		return response
	} catch (error) {
		console.error("Failed to search definitions:", error)

		// Return empty response if service is not available
		return SearchDefinitionsResponse.create({
			definitions: [],
		})
	}
}
