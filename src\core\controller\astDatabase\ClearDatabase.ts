/**
 * Clear Database Controller
 */

import { Controller } from ".."
import { Empty } from "@shared/proto/common"
import type { ClearDatabaseRequest } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function ClearDatabase(controller: Controller, request: ClearDatabaseRequest): Promise<Empty> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.clearDatabase(request)

		return response
	} catch (error) {
		console.error("Failed to clear database:", error)
		throw error
	}
}
