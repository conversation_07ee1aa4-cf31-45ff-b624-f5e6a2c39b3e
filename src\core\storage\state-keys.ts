export type SecretKey =
	| "apiKey"
	| "clineApiKey"
	| "openRouterApiKey"
	| "awsAccessKey"
	| "awsSecretKey"
	| "awsSessionToken"
	| "openAiApiKey"
	| "geminiApiKey"
	| "openAiNativeApiKey"
	| "deepSeekApiKey"
	| "requestyApiKey"
	| "togetherApiKey"
	| "fireworksApiKey"
	| "qwenApiKey"
	| "doubaoApiKey"
	| "mistralApiKey"
	| "liteLlmApiKey"
	| "authNonce"
	| "asksageApiKey"
	| "xaiApiKey"
	| "nebiusApiKey"
	| "sambanovaApiKey"
	| "cerebrasApiKey"
	| "sapAiCoreClientId"
	| "sapAiCoreClientSecret"
	| "qaxApiKey"

export type GlobalStateKey =
	| "awsRegion"
	| "awsUseCrossRegionInference"
	| "awsBedrockUsePromptCache"
	| "awsBedrockEndpoint"
	| "awsProfile"
	| "awsUseProfile"
	| "vertexProjectId"
	| "vertexRegion"
	| "lastShownAnnouncementId"
	| "taskHistory"
	| "openAiBaseUrl"
	| "openAiModelId"
	| "openAiModelInfo"
	| "openAiHeaders"
	| "ollamaBaseUrl"
	| "ollamaApiOptionsCtxNum"
	| "lmStudioModelId"
	| "lmStudioBaseUrl"
	| "anthropicBaseUrl"
	| "geminiBaseUrl"
	| "azureApiVersion"
	| "openRouterProviderSorting"
	| "autoApprovalSettings"
	| "globalClineRulesToggles"
	| "globalWorkflowToggles"
	| "browserSettings"
	| "userInfo"
	| "liteLlmBaseUrl"
	| "liteLlmUsePromptCache"
	| "fireworksModelMaxCompletionTokens"
	| "fireworksModelMaxTokens"
	| "qwenApiLine"
	| "mcpMarketplaceCatalog"
	| "telemetrySetting"
	| "asksageApiUrl"
	| "planActSeparateModelsSetting"
	| "enableCheckpointsSetting"
	| "mcpMarketplaceEnabled"
	| "favoritedModelIds"
	| "requestTimeoutMs"
	| "shellIntegrationTimeout"
	| "mcpResponsesCollapsed"
	| "terminalReuseEnabled"
	| "defaultTerminalProfile"
	| "isNewUser"
	| "terminalOutputLineLimit"
	| "mcpRichDisplayEnabled"
	| "sapAiCoreTokenUrl"
	| "sapAiCoreBaseUrl"
	| "sapAiResourceGroup"
	| "sapAiCoreClientId"
	| "sapAiCoreClientSecret"
	| "sapAiCoreModelId"
	| "claudeCodePath"
	| "qaxBaseUrl"

export type LocalStateKey =
	| "localClineRulesToggles"
	| "chatSettings"
	// Current active model configuration (per workspace)
	| "apiProvider"
	| "apiModelId"
	| "thinkingBudgetTokens"
	| "reasoningEffort"
	| "vsCodeLmModelSelector"
	| "awsBedrockCustomSelected"
	| "awsBedrockCustomModelBaseId"
	| "openRouterModelId"
	| "openRouterModelInfo"
	| "openAiModelId"
	| "openAiModelInfo"
	| "ollamaModelId"
	| "lmStudioModelId"
	| "liteLlmModelId"
	| "liteLlmModelInfo"
	| "requestyModelId"
	| "requestyModelInfo"
	| "togetherModelId"
	| "fireworksModelId"
	| "qaxModelId"
	// Previous mode saved configurations (per workspace)
	| "previousModeApiProvider"
	| "previousModeModelId"
	| "previousModeModelInfo"
	| "previousModeVsCodeLmModelSelector"
	| "previousModeThinkingBudgetTokens"
	| "previousModeReasoningEffort"
	| "previousModeAwsBedrockCustomSelected"
	| "previousModeAwsBedrockCustomModelBaseId"
	| "previousModeSapAiCoreClientId"
	| "previousModeSapAiCoreClientSecret"
	| "previousModeSapAiCoreBaseUrl"
	| "previousModeSapAiCoreTokenUrl"
	| "previousModeSapAiCoreResourceGroup"
	| "previousModeSapAiCoreModelId"
