# Autocomplete 设置改进

## 新增功能

### 1. **Debounce 时间设置**

#### 配置接口扩展
```typescript
export interface AutocompleteSettings {
  // ... 其他字段
  debounceMs?: number  // 新增：防抖时间设置
}
```

#### 默认值
```typescript
export const DEFAULT_AUTOCOMPLETE_SETTINGS: AutocompleteSettings = {
  // ... 其他设置
  debounceMs: 300, // 默认 300ms 防抖
}
```

#### 验证规则
```typescript
if (settings.debounceMs && (settings.debounceMs < 0 || settings.debounceMs > 5000)) {
  errors.push("Debounce time must be between 0ms and 5000ms")
}
```

#### 动态使用
```typescript
// 从配置中获取防抖时间
const getDebounceMs = () => {
  const configManager = AutocompleteConfigManager.instance
  const settings = configManager.getSettings()
  return settings.debounceMs || UI_UPDATE_DEBOUNCE_MS
}

const debouncedGenerateCompletion = createDebouncedFn(generateCompletion, getDebounceMs())
const debouncedGenerateFimCompletion = createDebouncedFn(generateFimCompletion, getDebounceMs())
```

### 2. **修复 Temperature 和 Max Tokens 设置**

#### FIM Handler 接口扩展
```typescript
export interface FimHandlerOptions {
  apiKey: string
  baseUrl: string
  requestTimeoutMs?: number
  customHeaders?: Record<string, string>
  maxTokens?: number      // 新增
  temperature?: number    // 新增
}
```

#### 实际使用配置值
```typescript
// 非流式请求
const body = {
  prompt: limitedPrompt,
  suffix: limitedSuffix,
  stream: false,
  max_tokens: this.options.maxTokens || 100,     // 使用配置值
  temperature: this.options.temperature || 0.1,  // 使用配置值
}

// 流式请求
const body = {
  prompt: limitedPrompt,
  suffix: limitedSuffix,
  stream: true,
  max_tokens: this.options.maxTokens || 100,     // 使用配置值
  temperature: this.options.temperature || 0.1,  // 使用配置值
}
```

#### 传递配置参数
```typescript
// AutocompleteProvider 中创建 FIM Handler
fimHandler = buildFimHandler({
  apiKey: settings.fim.apiKey,
  baseUrl: settings.fim.baseUrl,
  requestTimeoutMs: settings.requestTimeoutMs,
  customHeaders: settings.customHeaders,
  maxTokens: settings.maxTokens,        // 传递 maxTokens
  temperature: settings.temperature,    // 传递 temperature
})
```

## UI 改进

### 新增 Debounce 时间设置字段
```tsx
<VSCodeTextField
  value={localSettings.debounceMs?.toString() || ""}
  style={{ width: "100%" }}
  onInput={(e) =>
    handleInputChange("debounceMs")(parseInt((e.target as HTMLInputElement)?.value) || 300)
  }
  placeholder="300">
  <span style={{ fontWeight: 500 }}>Debounce Time (ms)</span>
</VSCodeTextField>
```

### 配置管理
```typescript
// 加载配置
debounceMs: config.get("debounceMs", DEFAULT_AUTOCOMPLETE_SETTINGS.debounceMs),

// 保存配置
if (settings.debounceMs !== undefined) {
  await config.update("debounceMs", settings.debounceMs, vscode.ConfigurationTarget.Global)
}
```

## Package.json 配置

```json
{
  "cline.autocomplete.debounceMs": {
    "type": "number",
    "default": 300,
    "minimum": 0,
    "maximum": 5000,
    "description": "Debounce time in milliseconds for autocomplete requests"
  }
}
```

## 功能效果

### 1. **可调节的响应速度**
- **低 debounce (50-100ms)**: 更快的响应，但可能增加 API 调用频率
- **中等 debounce (300ms)**: 平衡的响应速度和 API 调用频率（默认）
- **高 debounce (500-1000ms)**: 减少 API 调用，但响应稍慢

### 2. **正确的模型参数**
- **Temperature**: 现在会使用用户配置的值控制生成的随机性
- **Max Tokens**: 现在会使用用户配置的值限制生成长度

### 3. **更好的用户体验**
- 用户可以根据网络环境和使用习惯调整防抖时间
- 模型参数设置现在真正生效
- 更精确的控制代码补全行为

## 使用建议

### Debounce 时间调整
- **快速网络 + 高性能服务器**: 100-200ms
- **一般网络环境**: 300-500ms（推荐）
- **慢速网络 + 节省 API 调用**: 500-1000ms

### Temperature 设置
- **0.0-0.2**: 更确定性的补全（推荐用于代码）
- **0.3-0.7**: 平衡创造性和确定性
- **0.8-1.0**: 更有创造性但可能不太准确

### Max Tokens 设置
- **50-100**: 短补全，适合单行或简单语句
- **100-200**: 中等补全，适合函数体或代码块
- **200+**: 长补全，适合复杂逻辑或多行代码

现在所有的 autocomplete 设置都能正确生效，用户可以根据需要精确调整补全行为！
